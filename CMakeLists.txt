# 银行取号叫号系统 CMakeLists.txt
# 适用于Qt 4.8.5

cmake_minimum_required(VERSION 2.8.12)
project(BankQueueSystem)

# 设置C++标准（避免C++11特性）
set(CMAKE_CXX_STANDARD 98)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt4
find_package(Qt4 REQUIRED COMPONENTS QtCore QtGui QtNetwork)
include(${QT_USE_FILE})

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/common)

# 编译选项
if(CMAKE_COMPILER_IS_GNUCXX)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -std=c++98")
endif()

# 公共库
set(COMMON_SOURCES
    common/ticket.cpp
    common/queue_manager.cpp
    common/message_protocol.cpp
    common/network_client.cpp
    common/data_manager.cpp
)

set(COMMON_HEADERS
    common/ticket.h
    common/queue_manager.h
    common/message_protocol.h
    common/network_client.h
    common/data_manager.h
)

# 生成MOC文件
qt4_wrap_cpp(COMMON_MOC_SOURCES ${COMMON_HEADERS})

# 创建公共静态库
add_library(common STATIC ${COMMON_SOURCES} ${COMMON_MOC_SOURCES})
target_link_libraries(common ${QT_LIBRARIES})

# 取号机应用程序
set(TICKET_MACHINE_SOURCES
    ticket_machine/main.cpp
    ticket_machine/ticket_machine_window.cpp
)

set(TICKET_MACHINE_HEADERS
    ticket_machine/ticket_machine_window.h
)

qt4_wrap_cpp(TICKET_MACHINE_MOC_SOURCES ${TICKET_MACHINE_HEADERS})

add_executable(ticket_machine 
    ${TICKET_MACHINE_SOURCES} 
    ${TICKET_MACHINE_MOC_SOURCES}
)

target_link_libraries(ticket_machine common ${QT_LIBRARIES})

# 叫号机应用程序
set(CALLING_MACHINE_SOURCES
    calling_machine/main.cpp
    calling_machine/calling_machine_window.cpp
)

set(CALLING_MACHINE_HEADERS
    calling_machine/calling_machine_window.h
)

qt4_wrap_cpp(CALLING_MACHINE_MOC_SOURCES ${CALLING_MACHINE_HEADERS})

add_executable(calling_machine 
    ${CALLING_MACHINE_SOURCES} 
    ${CALLING_MACHINE_MOC_SOURCES}
)

target_link_libraries(calling_machine common ${QT_LIBRARIES})

# 创建数据目录
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/bin/data)
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/bin/data/backup)

# 安装规则
install(TARGETS ticket_machine calling_machine
    RUNTIME DESTINATION bin
)

install(TARGETS common
    ARCHIVE DESTINATION lib
)

install(FILES ${COMMON_HEADERS}
    DESTINATION include
)

# 打包配置
set(CPACK_PACKAGE_NAME "BankQueueSystem")
set(CPACK_PACKAGE_VERSION "1.0.0")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "Bank Queue Management System")
set(CPACK_PACKAGE_VENDOR "BankQueueSystem")

include(CPack)
