#include "network_client.h"
#include <QDebug>

NetworkClient::NetworkClient(QObject *parent)
    : QObject(parent)
    , m_socket(new QTcpSocket(this))
    , m_state(DISCONNECTED)
    , m_clientType(MessageProtocol::TICKET_MACHINE)
    , m_clientId(0)
    , m_registered(false)
    , m_heartbeatTimer(new QTimer(this))
    , m_heartbeatInterval(30000) // 30秒心跳间隔
{
    // 连接socket信号
    connect(m_socket, SIGNAL(connected()), this, SLOT(onConnected()));
    connect(m_socket, SIGNAL(disconnected()), this, SLOT(onDisconnected()));
    connect(m_socket, SIGNAL(error(QAbstractSocket::SocketError)), this, SLOT(onError(QAbstractSocket::SocketError)));
    connect(m_socket, SIGNAL(readyRead()), this, SLOT(onReadyRead()));
    
    // 连接心跳定时器
    connect(m_heartbeatTimer, SIGNAL(timeout()), this, SLOT(onHeartbeatTimeout()));
}

NetworkClient::~NetworkClient()
{
    if (m_registered) {
        unregisterClient();
    }
    disconnectFromHost();
}

void NetworkClient::connectToHost(const QString& hostAddress, quint16 port)
{
    if (m_state == CONNECTED || m_state == CONNECTING) {
        return;
    }
    
    setState(CONNECTING);
    m_socket->connectToHost(hostAddress, port);
}

void NetworkClient::disconnectFromHost()
{
    if (m_registered) {
        unregisterClient();
    }
    
    stopHeartbeat();
    
    if (m_socket->state() != QAbstractSocket::UnconnectedState) {
        m_socket->disconnectFromHost();
    }
    
    setState(DISCONNECTED);
}

bool NetworkClient::isConnected() const
{
    return m_state == CONNECTED;
}

NetworkClient::ConnectionState NetworkClient::getConnectionState() const
{
    return m_state;
}

void NetworkClient::registerClient(MessageProtocol::ClientType clientType, int clientId, const QString& clientName)
{
    m_clientType = clientType;
    m_clientId = clientId;
    m_clientName = clientName;
    
    if (isConnected()) {
        QString message = MessageProtocol::buildClientRegister(clientType, clientId, clientName);
        if (sendMessage(message)) {
            m_registered = true;
        }
    }
}

void NetworkClient::unregisterClient()
{
    if (m_registered && isConnected()) {
        QString message = MessageProtocol::buildClientUnregister(m_clientType, m_clientId);
        sendMessage(message);
        m_registered = false;
    }
}

bool NetworkClient::sendMessage(const QString& message)
{
    if (!isConnected()) {
        return false;
    }
    
    QString fullMessage = message + "\n";
    qint64 bytesWritten = m_socket->write(fullMessage.toUtf8());
    
    if (bytesWritten == -1) {
        qDebug() << "Failed to send message:" << message;
        return false;
    }
    
    return m_socket->flush();
}

bool NetworkClient::sendTakeNumberRequest(Ticket::UserType userType, Ticket::BusinessType businessType)
{
    QString message = MessageProtocol::buildTakeNumberRequest(userType, businessType);
    return sendMessage(message);
}

bool NetworkClient::sendCallNextRequest(int windowId)
{
    QString message = MessageProtocol::buildCallNextRequest(windowId);
    return sendMessage(message);
}

bool NetworkClient::sendCallCurrentRequest(int windowId)
{
    QString message = MessageProtocol::buildCallCurrentRequest(windowId);
    return sendMessage(message);
}

bool NetworkClient::sendFinishCurrentRequest(int windowId)
{
    QString message = MessageProtocol::buildFinishCurrentRequest(windowId);
    return sendMessage(message);
}

bool NetworkClient::sendQueueStatusRequest()
{
    QString message = MessageProtocol::buildQueueStatusRequest();
    return sendMessage(message);
}

bool NetworkClient::sendHeartbeat()
{
    QString message = MessageProtocol::buildHeartbeat(m_clientType, m_clientId);
    return sendMessage(message);
}

bool NetworkClient::broadcastTicketAdded(const Ticket& ticket)
{
    QString message = MessageProtocol::buildTicketAddedBroadcast(ticket);
    return sendMessage(message);
}

bool NetworkClient::broadcastTicketCalled(const Ticket& ticket, int windowId)
{
    QString message = MessageProtocol::buildTicketCalledBroadcast(ticket, windowId);
    return sendMessage(message);
}

bool NetworkClient::broadcastTicketFinished(const Ticket& ticket, int windowId)
{
    QString message = MessageProtocol::buildTicketFinishedBroadcast(ticket, windowId);
    return sendMessage(message);
}

bool NetworkClient::broadcastTicketExpired(const Ticket& ticket, int windowId)
{
    QString message = MessageProtocol::buildTicketExpiredBroadcast(ticket, windowId);
    return sendMessage(message);
}

bool NetworkClient::broadcastQueueChanged()
{
    QString message = MessageProtocol::buildQueueChangedBroadcast();
    return sendMessage(message);
}

void NetworkClient::setClientInfo(MessageProtocol::ClientType clientType, int clientId, const QString& clientName)
{
    m_clientType = clientType;
    m_clientId = clientId;
    m_clientName = clientName;
}

void NetworkClient::setHeartbeatInterval(int intervalMs)
{
    m_heartbeatInterval = intervalMs;
    if (m_heartbeatTimer->isActive()) {
        m_heartbeatTimer->setInterval(intervalMs);
    }
}

void NetworkClient::startHeartbeat()
{
    if (isConnected() && !m_heartbeatTimer->isActive()) {
        m_heartbeatTimer->setInterval(m_heartbeatInterval);
        m_heartbeatTimer->start();
    }
}

void NetworkClient::stopHeartbeat()
{
    if (m_heartbeatTimer->isActive()) {
        m_heartbeatTimer->stop();
    }
}

void NetworkClient::onConnected()
{
    setState(CONNECTED);
    emit connected();
    
    // 自动注册客户端
    if (!m_clientName.isEmpty()) {
        registerClient(m_clientType, m_clientId, m_clientName);
    }
    
    // 开始心跳
    startHeartbeat();
}

void NetworkClient::onDisconnected()
{
    setState(DISCONNECTED);
    m_registered = false;
    stopHeartbeat();
    emit disconnected();
}

void NetworkClient::onError(QAbstractSocket::SocketError error)
{
    Q_UNUSED(error);
    setState(ERROR);
    QString errorString = m_socket->errorString();
    qDebug() << "Network error:" << errorString;
    emit connectionError(errorString);
}

void NetworkClient::onReadyRead()
{
    QByteArray data = m_socket->readAll();
    m_receiveBuffer.append(QString::fromUtf8(data));
    
    while (hasCompleteMessage()) {
        QString message = extractCompleteMessage();
        if (!message.isEmpty()) {
            processMessage(message);
            emit messageReceived(message);
        }
    }
}

void NetworkClient::onHeartbeatTimeout()
{
    sendHeartbeat();
}

void NetworkClient::setState(ConnectionState state)
{
    if (m_state != state) {
        m_state = state;
        emit stateChanged(state);
    }
}

void NetworkClient::processMessage(const QString& message)
{
    MessageProtocol::MessageType type;
    QStringList params;

    if (!MessageProtocol::parseMessage(message, type, params)) {
        qDebug() << "Failed to parse message:" << message;
        return;
    }

    switch (type) {
    case MessageProtocol::TAKE_NUMBER_RESPONSE:
        handleTakeNumberResponse(params);
        break;
    case MessageProtocol::CALL_NEXT_RESPONSE:
        handleCallNextResponse(params);
        break;
    case MessageProtocol::CALL_CURRENT_RESPONSE:
        handleCallCurrentResponse(params);
        break;
    case MessageProtocol::FINISH_CURRENT_RESPONSE:
        handleFinishCurrentResponse(params);
        break;
    case MessageProtocol::QUEUE_STATUS_RESPONSE:
        handleQueueStatusResponse(params);
        break;
    case MessageProtocol::TICKET_ADDED_BROADCAST:
        handleTicketAddedBroadcast(params);
        break;
    case MessageProtocol::TICKET_CALLED_BROADCAST:
        handleTicketCalledBroadcast(params);
        break;
    case MessageProtocol::TICKET_FINISHED_BROADCAST:
        handleTicketFinishedBroadcast(params);
        break;
    case MessageProtocol::TICKET_EXPIRED_BROADCAST:
        handleTicketExpiredBroadcast(params);
        break;
    case MessageProtocol::QUEUE_CHANGED_BROADCAST:
        handleQueueChangedBroadcast(params);
        break;
    case MessageProtocol::CLIENT_REGISTER:
        handleClientRegister(params);
        break;
    case MessageProtocol::CLIENT_UNREGISTER:
        handleClientUnregister(params);
        break;
    case MessageProtocol::HEARTBEAT:
        handleHeartbeat(params);
        break;
    case MessageProtocol::ERROR_RESPONSE:
        handleErrorResponse(params);
        break;
    default:
        qDebug() << "Unknown message type:" << static_cast<int>(type);
        break;
    }
}

void NetworkClient::handleTakeNumberResponse(const QStringList& params)
{
    Ticket ticket;
    bool success;
    QString error;

    if (MessageProtocol::parseTakeNumberResponse(params, ticket, success, error)) {
        emit takeNumberResponse(ticket, success, error);
    }
}

void NetworkClient::handleCallNextResponse(const QStringList& params)
{
    Ticket ticket;
    bool success;
    QString error;

    if (MessageProtocol::parseCallNextResponse(params, ticket, success, error)) {
        emit callNextResponse(ticket, success, error);
    }
}

void NetworkClient::handleCallCurrentResponse(const QStringList& params)
{
    bool success;
    QString error;

    if (MessageProtocol::parseCallCurrentResponse(params, success, error)) {
        emit callCurrentResponse(success, error);
    }
}

void NetworkClient::handleFinishCurrentResponse(const QStringList& params)
{
    bool success;
    QString error;

    if (MessageProtocol::parseFinishCurrentResponse(params, success, error)) {
        emit finishCurrentResponse(success, error);
    }
}

void NetworkClient::handleQueueStatusResponse(const QStringList& params)
{
    int waitingCount;
    QList<Ticket> tickets;

    if (MessageProtocol::parseQueueStatusResponse(params, waitingCount, tickets)) {
        emit queueStatusResponse(waitingCount, tickets);
    }
}

void NetworkClient::handleTicketAddedBroadcast(const QStringList& params)
{
    if (params.size() >= 1) {
        Ticket ticket;
        if (MessageProtocol::decodeTicket(params.at(0), ticket)) {
            emit ticketAddedBroadcast(ticket);
        }
    }
}

void NetworkClient::handleTicketCalledBroadcast(const QStringList& params)
{
    Ticket ticket;
    int windowId;

    if (MessageProtocol::parseTicketBroadcast(params, ticket, windowId)) {
        emit ticketCalledBroadcast(ticket, windowId);
    }
}

void NetworkClient::handleTicketFinishedBroadcast(const QStringList& params)
{
    Ticket ticket;
    int windowId;

    if (MessageProtocol::parseTicketBroadcast(params, ticket, windowId)) {
        emit ticketFinishedBroadcast(ticket, windowId);
    }
}

void NetworkClient::handleTicketExpiredBroadcast(const QStringList& params)
{
    Ticket ticket;
    int windowId;

    if (MessageProtocol::parseTicketBroadcast(params, ticket, windowId)) {
        emit ticketExpiredBroadcast(ticket, windowId);
    }
}

void NetworkClient::handleQueueChangedBroadcast(const QStringList& params)
{
    Q_UNUSED(params);
    emit queueChangedBroadcast();
}

void NetworkClient::handleClientRegister(const QStringList& params)
{
    MessageProtocol::ClientType clientType;
    int clientId;
    QString clientName;

    if (MessageProtocol::parseClientRegister(params, clientType, clientId, clientName)) {
        emit clientRegistered(clientType, clientId, clientName);
    }
}

void NetworkClient::handleClientUnregister(const QStringList& params)
{
    MessageProtocol::ClientType clientType;
    int clientId;

    if (MessageProtocol::parseClientUnregister(params, clientType, clientId)) {
        emit clientUnregistered(clientType, clientId);
    }
}

void NetworkClient::handleHeartbeat(const QStringList& params)
{
    MessageProtocol::ClientType clientType;
    int clientId;

    if (MessageProtocol::parseHeartbeat(params, clientType, clientId)) {
        emit heartbeatReceived(clientType, clientId);
    }
}

void NetworkClient::handleErrorResponse(const QStringList& params)
{
    if (params.size() >= 1) {
        emit errorResponse(params.at(0));
    }
}

QString NetworkClient::extractCompleteMessage()
{
    int newlineIndex = m_receiveBuffer.indexOf('\n');
    if (newlineIndex == -1) {
        return QString();
    }

    QString message = m_receiveBuffer.left(newlineIndex);
    m_receiveBuffer.remove(0, newlineIndex + 1);

    return message.trimmed();
}

bool NetworkClient::hasCompleteMessage() const
{
    return m_receiveBuffer.contains('\n');
}
