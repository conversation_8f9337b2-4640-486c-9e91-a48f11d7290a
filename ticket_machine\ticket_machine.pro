# 取号机项目文件
# 适用于Qt 4.8.5

TEMPLATE = app
TARGET = ticket_machine

# Qt模块
QT += core gui network

# 编译器设置
QMAKE_CXXFLAGS += -std=c++98  # 避免使用C++11特性

# 源文件
SOURCES += \
    main.cpp \
    ticket_machine_window.cpp

# 头文件
HEADERS += \
    ticket_machine_window.h

# 包含路径
INCLUDEPATH += ../common

# 链接库
LIBS += -L../lib -lcommon

# 输出目录
DESTDIR = ../bin
OBJECTS_DIR = ../build/ticket_machine
MOC_DIR = ../build/ticket_machine
UI_DIR = ../build/ticket_machine

# 资源文件（如果有的话）
# RESOURCES += resources.qrc

# 图标（如果有的话）
# RC_ICONS = icon.ico

# 安装规则
target.path = ../bin
INSTALLS += target

# 运行时依赖
QMAKE_POST_LINK += $$quote(mkdir -p ../bin/data)

# 清理规则
QMAKE_CLEAN += ../bin/ticket_machine
