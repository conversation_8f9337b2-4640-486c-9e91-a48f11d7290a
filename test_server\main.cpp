#include <QCoreApplication>
#include <QTcpServer>
#include <QTcpSocket>
#include <QDebug>
#include <QTimer>

class SimpleServer : public QObject
{
    Q_OBJECT

public:
    SimpleServer(QObject* parent = 0) : QObject(parent)
    {
        m_server = new QTcpServer(this);
        connect(m_server, SIGNAL(newConnection()), this, SLOT(onNewConnection()));
        
        if (m_server->listen(QHostAddress::Any, 8889)) {
            qDebug() << "Server started on port 8889";
            qDebug() << "Waiting for connections...";
        } else {
            qDebug() << "Failed to start server:" << m_server->errorString();
        }
    }

private slots:
    void onNewConnection()
    {
        QTcpSocket* socket = m_server->nextPendingConnection();
        qDebug() << "New connection from:" << socket->peerAddress().toString();
        
        connect(socket, SIGNAL(readyRead()), this, SLOT(onDataReceived()));
        connect(socket, SIGNAL(disconnected()), socket, SLOT(deleteLater()));
        
        m_clients.append(socket);
    }
    
    void onDataReceived()
    {
        QTcpSocket* socket = qobject_cast<QTcpSocket*>(sender());
        if (!socket) return;
        
        QByteArray data = socket->readAll();
        qDebug() << "Received data from" << socket->peerAddress().toString() << ":" << data;
        
        // Echo back the data
        socket->write("ACK: " + data);
    }

private:
    QTcpServer* m_server;
    QList<QTcpSocket*> m_clients;
};

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "Starting Bank Queue System Test Server...";
    
    SimpleServer server;
    
    return app.exec();
}

#include "main.moc"
