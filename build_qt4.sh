#!/bin/bash

# 银行取号叫号系统编译脚本 - Qt 4.8.5 专用版本
# 修复了Qt 4.8.5兼容性问题

echo "=== 银行取号叫号系统编译脚本 (Qt 4.8.5) ==="
echo "已修复Qt 4.8.5兼容性问题"
echo ""

# 检查Qt环境
if ! command -v qmake &> /dev/null; then
    echo "错误: 未找到qmake命令，请确保Qt 4.8.5已正确安装"
    echo "Ubuntu 18.04安装命令:"
    echo "  sudo apt-get install qt4-dev-tools libqt4-dev libqt4-core libqt4-gui libqt4-network"
    exit 1
fi

# 显示Qt版本
echo "当前Qt版本:"
qmake -version
echo ""

# 检查Qt版本是否为4.x
QT_VERSION=$(qmake -version | grep "Using Qt version" | cut -d' ' -f4)
if [[ ! $QT_VERSION =~ ^4\. ]]; then
    echo "警告: 检测到Qt版本为 $QT_VERSION，本项目专为Qt 4.8.5设计"
    echo "可能会出现兼容性问题"
    read -p "是否继续编译? (y/N): " continue_build
    if [[ $continue_build != "y" && $continue_build != "Y" ]]; then
        exit 1
    fi
fi

# 创建必要的目录
echo "创建编译目录..."
mkdir -p build
mkdir -p bin
mkdir -p lib
mkdir -p bin/data
mkdir -p bin/data/backup

# 清理之前的编译结果
echo "清理之前的编译结果..."
make clean 2>/dev/null || true
rm -f Makefile
rm -rf build/*
rm -f bin/ticket_machine bin/calling_machine
rm -f lib/libcommon.a

echo "开始编译..."

# 生成Makefile
echo "生成Makefile..."
qmake bank_queue_system.pro

if [ $? -ne 0 ]; then
    echo "错误: qmake失败"
    exit 1
fi

# 编译项目
echo "编译项目..."
echo "注意: 如果出现QString相关警告，这是正常的Qt 4.8.5兼容性调整"
echo ""

# 分步编译以确保依赖关系正确
echo "步骤1: 编译公共库..."
cd common
qmake
make
if [ $? -ne 0 ]; then
    echo "错误: 公共库编译失败"
    cd ..
    exit 1
fi
cd ..

echo "步骤2: 编译取号机..."
cd ticket_machine
qmake
make
if [ $? -ne 0 ]; then
    echo "错误: 取号机编译失败"
    cd ..
    exit 1
fi
cd ..

echo "步骤3: 编译叫号机..."
cd calling_machine
qmake
make
if [ $? -ne 0 ]; then
    echo "错误: 叫号机编译失败"
    cd ..
    exit 1
fi
cd ..

# 检查编译结果
echo ""
echo "=== 编译完成，检查结果 ==="
echo ""
echo "可执行文件:"
if [ -f "bin/ticket_machine" ]; then
    echo "  ✓ 取号机: bin/ticket_machine"
else
    echo "  ✗ 取号机编译失败"
fi

if [ -f "bin/calling_machine" ]; then
    echo "  ✓ 叫号机: bin/calling_machine"
else
    echo "  ✗ 叫号机编译失败"
fi

if [ -f "lib/libcommon.a" ]; then
    echo "  ✓ 公共库: lib/libcommon.a"
else
    echo "  ✗ 公共库编译失败"
fi

# 检查是否所有文件都编译成功
if [ -f "lib/libcommon.a" ] && [ -f "bin/ticket_machine" ] && [ -f "bin/calling_machine" ]; then
    
    echo ""
    echo "=== 编译成功! ==="
    echo ""
    echo "运行方法:"
    echo "  ./bin/ticket_machine                # 启动取号机"
    echo "  ./bin/calling_machine 1             # 启动1号窗口叫号机"
    echo "  ./bin/calling_machine 2             # 启动2号窗口叫号机"
    echo ""
    echo "测试方法:"
    echo "  chmod +x test_system.sh"
    echo "  ./test_system.sh"
    echo ""
    echo "注意事项:"
    echo "  - 确保网络配置正确（默认使用127.0.0.1:8888）"
    echo "  - 数据文件将保存在 bin/data/ 目录"
    echo "  - 备份文件将保存在 bin/data/backup/ 目录"

else
    echo ""
    echo "=== 编译失败! ==="
    echo ""
    echo "常见问题解决方法:"
    echo "1. 检查Qt版本是否为4.8.5:"
    echo "   qmake -version"
    echo ""
    echo "2. 检查Qt开发包是否完整安装:"
    echo "   sudo apt-get install qt4-dev-tools libqt4-dev libqt4-core libqt4-gui libqt4-network"
    echo ""
    echo "3. 清理后重新编译:"
    echo "   make clean"
    echo "   qmake"
    echo "   make"
    echo ""
    echo "4. 查看详细错误信息:"
    echo "   make 2>&1 | tee build.log"
    
    exit 1
fi

echo ""
echo "=== 编译完成 ==="
