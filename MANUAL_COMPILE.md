# 手动编译指南

## 问题说明
主项目 `bank_queue_system.pro` 是一个 SUBDIRS 项目，它只是组织子项目的容器，不会直接生成可执行文件。需要分别编译各个子项目。

## 手动编译步骤

### 方法1: 使用修复后的编译脚本（推荐）
```bash
# 清理之前的编译结果
make clean
rm -rf bin lib build
rm -f Makefile */Makefile

# 使用修复后的编译脚本
chmod +x build_qt4.sh
./build_qt4.sh
```

### 方法2: 手动分步编译
```bash
# 1. 清理环境
make clean 2>/dev/null || true
rm -rf bin lib build
rm -f Makefile */Makefile

# 2. 创建必要目录
mkdir -p bin lib build

# 3. 编译公共库
cd common
qmake
make
cd ..

# 4. 编译取号机
cd ticket_machine  
qmake
make
cd ..

# 5. 编译叫号机
cd calling_machine
qmake  
make
cd ..

# 6. 检查结果
ls -la bin/
ls -la lib/
```

### 方法3: 使用主项目文件编译
```bash
# 清理
make clean
rm -f Makefile

# 生成Makefile
qmake bank_queue_system.pro

# 编译所有子项目
make

# 如果上面失败，尝试强制编译
make -j1  # 单线程编译
```

## 预期结果

编译成功后应该看到：
```
bin/
├── ticket_machine      # 取号机可执行文件
└── calling_machine     # 叫号机可执行文件

lib/
└── libcommon.a         # 公共静态库
```

## 常见问题排除

### 1. 链接错误
如果出现链接错误，通常是因为公共库没有正确编译：
```bash
# 重新编译公共库
cd common
make clean
qmake
make
cd ..
```

### 2. 找不到头文件
检查包含路径是否正确：
```bash
# 在 ticket_machine 或 calling_machine 目录中
qmake -query
```

### 3. Qt模块问题
确保Qt 4.8.5完整安装：
```bash
# 检查Qt版本
qmake -version

# 检查Qt模块
pkg-config --list-all | grep -i qt
```

### 4. 权限问题
确保有写入权限：
```bash
chmod 755 .
chmod -R 644 *.cpp *.h *.pro
chmod 755 *.sh
```

## 调试编译问题

### 查看详细编译信息
```bash
# 在各个子目录中运行
qmake
make VERBOSE=1
```

### 检查Makefile生成
```bash
# 检查是否正确生成了Makefile
ls -la */Makefile
```

### 检查依赖关系
```bash
# 检查库文件是否存在
file lib/libcommon.a

# 检查可执行文件依赖
ldd bin/ticket_machine 2>/dev/null || echo "静态链接或文件不存在"
```

## 成功编译后的测试

### 1. 基本测试
```bash
# 检查文件是否可执行
file bin/ticket_machine
file bin/calling_machine

# 尝试运行（会显示帮助或错误信息）
./bin/ticket_machine --help 2>/dev/null || echo "程序存在"
./bin/calling_machine --help 2>/dev/null || echo "程序存在"
```

### 2. 完整测试
```bash
# 使用测试脚本
chmod +x test_system.sh
./test_system.sh
```

## 如果仍然失败

1. **检查Qt安装**：
   ```bash
   sudo apt-get install qt4-dev-tools libqt4-dev libqt4-core libqt4-gui libqt4-network
   ```

2. **查看编译日志**：
   ```bash
   make 2>&1 | tee compile.log
   ```

3. **检查系统环境**：
   ```bash
   echo $QTDIR
   echo $PATH
   which qmake
   ```

4. **尝试最小化编译**：
   ```bash
   # 只编译公共库测试
   cd common
   qmake
   make
   ```

## 联系支持

如果按照以上步骤仍然无法编译，请提供：
1. Qt版本信息 (`qmake -version`)
2. 系统信息 (`uname -a`)
3. 完整的编译错误日志
4. 目录结构 (`find . -name "*.pro"`)

这样可以更好地诊断问题。
