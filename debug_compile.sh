#!/bin/bash

# 编译调试脚本 - 检查编译结果和问题
echo "=== 编译状态调试 ==="
echo ""

# 1. 检查当前目录结构
echo "1. 当前目录结构:"
pwd
ls -la
echo ""

# 2. 查找所有可执行文件
echo "2. 查找所有可能的可执行文件:"
echo "在整个项目中查找可执行文件..."
find . -type f -executable -name "*machine*" 2>/dev/null || echo "未找到machine相关的可执行文件"
find . -type f -name "ticket_machine" 2>/dev/null || echo "未找到ticket_machine"
find . -type f -name "calling_machine" 2>/dev/null || echo "未找到calling_machine"
echo ""

# 3. 查找库文件
echo "3. 查找库文件:"
find . -name "*.a" -o -name "*.so" 2>/dev/null || echo "未找到库文件"
echo ""

# 4. 检查各个子目录的编译结果
echo "4. 检查各子目录:"
echo ""

echo "common/ 目录:"
if [ -d "common" ]; then
    cd common
    pwd
    ls -la
    echo "查找编译产物:"
    find . -name "*.o" -o -name "*.a" 2>/dev/null || echo "未找到编译产物"
    cd ..
else
    echo "common目录不存在"
fi
echo ""

echo "ticket_machine/ 目录:"
if [ -d "ticket_machine" ]; then
    cd ticket_machine
    pwd
    ls -la
    echo "查找可执行文件:"
    find . -type f -executable 2>/dev/null || echo "未找到可执行文件"
    cd ..
else
    echo "ticket_machine目录不存在"
fi
echo ""

echo "calling_machine/ 目录:"
if [ -d "calling_machine" ]; then
    cd calling_machine
    pwd
    ls -la
    echo "查找可执行文件:"
    find . -type f -executable 2>/dev/null || echo "未找到可执行文件"
    cd ..
else
    echo "calling_machine目录不存在"
fi
echo ""

# 5. 检查是否存在bin和lib目录
echo "5. 检查输出目录:"
if [ -d "bin" ]; then
    echo "bin/ 目录存在:"
    ls -la bin/
else
    echo "bin/ 目录不存在，尝试创建..."
    mkdir -p bin
    echo "bin/ 目录已创建"
fi
echo ""

if [ -d "lib" ]; then
    echo "lib/ 目录存在:"
    ls -la lib/
else
    echo "lib/ 目录不存在，尝试创建..."
    mkdir -p lib
    echo "lib/ 目录已创建"
fi
echo ""

# 6. 检查Makefile是否存在
echo "6. 检查Makefile:"
echo "主目录Makefile:"
if [ -f "Makefile" ]; then
    echo "✅ 主目录Makefile存在"
else
    echo "❌ 主目录Makefile不存在"
fi

echo "子目录Makefile:"
for dir in common ticket_machine calling_machine; do
    if [ -f "$dir/Makefile" ]; then
        echo "✅ $dir/Makefile存在"
    else
        echo "❌ $dir/Makefile不存在"
    fi
done
echo ""

# 7. 检查.pro文件配置
echo "7. 检查项目配置:"
echo "ticket_machine.pro中的DESTDIR配置:"
if [ -f "ticket_machine/ticket_machine.pro" ]; then
    grep -n "DESTDIR" ticket_machine/ticket_machine.pro || echo "未找到DESTDIR配置"
else
    echo "ticket_machine.pro不存在"
fi

echo "calling_machine.pro中的DESTDIR配置:"
if [ -f "calling_machine/calling_machine.pro" ]; then
    grep -n "DESTDIR" calling_machine/calling_machine.pro || echo "未找到DESTDIR配置"
else
    echo "calling_machine.pro不存在"
fi

echo "common.pro中的DESTDIR配置:"
if [ -f "common/common.pro" ]; then
    grep -n "DESTDIR" common/common.pro || echo "未找到DESTDIR配置"
else
    echo "common.pro不存在"
fi
echo ""

# 8. 尝试手动查找编译产物
echo "8. 深度搜索编译产物:"
echo "搜索所有可能的可执行文件:"
find . -type f -perm -111 -name "*machine*" 2>/dev/null | head -10
echo ""
echo "搜索所有.o文件:"
find . -name "*.o" 2>/dev/null | head -5
echo ""
echo "搜索所有库文件:"
find . -name "lib*.a" 2>/dev/null
echo ""

echo "=== 调试完成 ==="
echo ""
echo "建议的下一步操作:"
echo "1. 如果找到了可执行文件但不在bin/目录，需要修复输出路径"
echo "2. 如果完全没有找到可执行文件，需要重新编译"
echo "3. 如果有编译错误，需要查看具体的错误信息"
