#!/bin/bash

# 银行取号叫号系统编译脚本
# 适用于Ubuntu 18.04 + Qt 4.8.5

echo "=== 银行取号叫号系统编译脚本 ==="
echo "Qt版本要求: 4.8.5"
echo "系统要求: Ubuntu 18.04"
echo ""

# 检查Qt环境
if ! command -v qmake &> /dev/null; then
    echo "错误: 未找到qmake命令，请确保Qt 4.8.5已正确安装"
    exit 1
fi

# 显示Qt版本
echo "当前Qt版本:"
qmake -version
echo ""

# 创建必要的目录
echo "创建编译目录..."
mkdir -p build
mkdir -p bin
mkdir -p lib
mkdir -p bin/data
mkdir -p bin/data/backup

# 选择编译方式
echo "请选择编译方式:"
echo "1) 使用qmake (推荐)"
echo "2) 使用cmake"
read -p "请输入选择 (1 或 2): " choice

case $choice in
    1)
        echo "使用qmake编译..."
        
        # 生成Makefile
        qmake bank_queue_system.pro
        
        # 编译
        make clean
        make -j$(nproc)
        
        if [ $? -eq 0 ]; then
            echo ""
            echo "=== 编译成功! ==="
            echo "可执行文件位置:"
            echo "  取号机: bin/ticket_machine"
            echo "  叫号机: bin/calling_machine"
            echo ""
            echo "运行方法:"
            echo "  ./bin/ticket_machine"
            echo "  ./bin/calling_machine [窗口编号]"
            echo ""
            echo "示例:"
            echo "  ./bin/calling_machine 1  # 1号窗口"
            echo "  ./bin/calling_machine 2  # 2号窗口"
        else
            echo "编译失败!"
            exit 1
        fi
        ;;
    2)
        echo "使用cmake编译..."
        
        cd build
        
        # 生成Makefile
        cmake ..
        
        # 编译
        make clean
        make -j$(nproc)
        
        if [ $? -eq 0 ]; then
            echo ""
            echo "=== 编译成功! ==="
            echo "可执行文件位置:"
            echo "  取号机: build/bin/ticket_machine"
            echo "  叫号机: build/bin/calling_machine"
            echo ""
            echo "运行方法:"
            echo "  cd build"
            echo "  ./bin/ticket_machine"
            echo "  ./bin/calling_machine [窗口编号]"
        else
            echo "编译失败!"
            exit 1
        fi
        
        cd ..
        ;;
    *)
        echo "无效选择!"
        exit 1
        ;;
esac

echo ""
echo "=== 编译完成 ==="
