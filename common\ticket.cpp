#include "ticket.h"
#include <QStringList>
#include <QDebug>

Ticket::Ticket()
    : m_userType(NORMAL)
    , m_businessType(SAVINGS)
    , m_number(0)
    , m_status(WAITING)
    , m_callCount(0)
{
    init();
}

Ticket::Ticket(UserType userType, BusinessType businessType, int number)
    : m_userType(userType)
    , m_businessType(businessType)
    , m_number(number)
    , m_status(WAITING)
    , m_callCount(0)
{
    init();
}

Ticket::Ticket(const QString& ticketNumber)
    : m_status(WAITING)
    , m_callCount(0)
{
    if (isValidTicketNumber(ticketNumber)) {
        m_userType = parseUserType(ticketNumber);
        m_businessType = parseBusinessType(ticketNumber);
        m_number = parseNumber(ticketNumber);
    } else {
        m_userType = NORMAL;
        m_businessType = SAVINGS;
        m_number = 0;
    }
    init();
}

void Ticket::init()
{
    m_createTime = QDateTime::currentDateTime();
    m_callTime = QDateTime();
}

QString Ticket::getTicketNumber() const
{
    return QString("%1%2%3")
        .arg(getUserTypeChar())
        .arg(getBusinessTypeChar())
        .arg(m_number, 3, 10, QChar('0'));
}

QString Ticket::getDisplayText() const
{
    return QString("%1%2业务%3号")
        .arg(getUserTypeText())
        .arg(getBusinessTypeText())
        .arg(m_number, 3, 10, QChar('0'));
}

QString Ticket::getBusinessTypeText() const
{
    switch (m_businessType) {
    case SAVINGS:
        return QString::fromUtf8("个人储蓄");
    case CONSULTING:
        return QString::fromUtf8("咨询");
    default:
        return QString::fromUtf8("未知");
    }
}

QString Ticket::getUserTypeText() const
{
    switch (m_userType) {
    case NORMAL:
        return QString::fromUtf8("普通");
    case VIP:
        return QString("VIP");
    default:
        return QString::fromUtf8("未知");
    }
}

void Ticket::setStatus(TicketStatus status)
{
    m_status = status;
}

void Ticket::setCallTime(const QDateTime& time)
{
    m_callTime = time;
}

void Ticket::incrementCallCount()
{
    m_callCount++;
}

bool Ticket::operator<(const Ticket& other) const
{
    // VIP优先级高于普通用户
    if (m_userType != other.m_userType) {
        return m_userType > other.m_userType; // VIP(1) > NORMAL(0)
    }
    
    // 同类型用户按号码顺序
    return m_number < other.m_number;
}

bool Ticket::operator==(const Ticket& other) const
{
    return getTicketNumber() == other.getTicketNumber();
}

QString Ticket::toString() const
{
    QStringList parts;
    parts.append(getTicketNumber());
    parts.append(QString::number(static_cast<int>(m_status)));
    parts.append(m_createTime.toString("yyyy-MM-dd hh:mm:ss"));
    parts.append(m_callTime.isValid() ? m_callTime.toString("yyyy-MM-dd hh:mm:ss") : "");
    parts.append(QString::number(m_callCount));

    return parts.join(QString("|"));
}

bool Ticket::fromString(const QString& str)
{
    QStringList parts = str.split(QString("|"));
    if (parts.size() < 5) {
        qDebug() << "Ticket::fromString: Not enough parts, expected at least 5, got" << parts.size();
        return false;
    }

    QString ticketNumber = parts.at(0);
    if (!isValidTicketNumber(ticketNumber)) {
        qDebug() << "Ticket::fromString: Invalid ticket number format:" << ticketNumber;
        return false;
    }

    m_userType = parseUserType(ticketNumber);
    m_businessType = parseBusinessType(ticketNumber);
    m_number = parseNumber(ticketNumber);
    m_status = static_cast<TicketStatus>(parts.at(1).toInt());
    m_createTime = QDateTime::fromString(parts.at(2), "yyyy-MM-dd hh:mm:ss");

    if (!parts.at(3).isEmpty()) {
        m_callTime = QDateTime::fromString(parts.at(3), "yyyy-MM-dd hh:mm:ss");
    }

    m_callCount = parts.at(4).toInt();

    return true;
}

char Ticket::getUserTypeChar() const
{
    return (m_userType == VIP) ? 'V' : 'C';
}

char Ticket::getBusinessTypeChar() const
{
    return (m_businessType == SAVINGS) ? 'A' : 'B';
}

bool Ticket::isValidTicketNumber(const QString& ticketNumber)
{
    if (ticketNumber.length() != 5) {
        return false;
    }
    
    QChar firstChar = ticketNumber.at(0);
    QChar secondChar = ticketNumber.at(1);
    
    // 检查首字母
    if (firstChar != 'C' && firstChar != 'V') {
        return false;
    }
    
    // 检查次字母
    if (secondChar != 'A' && secondChar != 'B') {
        return false;
    }
    
    // 检查后三位数字
    QString numberPart = ticketNumber.mid(2);
    bool ok;
    int number = numberPart.toInt(&ok);
    if (!ok || number < 1 || number > 999) {
        return false;
    }
    
    return true;
}

Ticket::UserType Ticket::parseUserType(const QString& ticketNumber)
{
    if (ticketNumber.length() > 0 && ticketNumber.at(0) == 'V') {
        return VIP;
    }
    return NORMAL;
}

Ticket::BusinessType Ticket::parseBusinessType(const QString& ticketNumber)
{
    if (ticketNumber.length() > 1 && ticketNumber.at(1) == 'B') {
        return CONSULTING;
    }
    return SAVINGS;
}

int Ticket::parseNumber(const QString& ticketNumber)
{
    if (ticketNumber.length() >= 5) {
        return ticketNumber.mid(2).toInt();
    }
    return 0;
}
