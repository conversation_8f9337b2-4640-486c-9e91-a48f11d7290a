#include "data_manager.h"
#include <QFile>
#include <QDir>
#include <QDebug>
#include <QDateTime>

DataManager::DataManager(QueueManager* queueManager, QObject *parent)
    : QObject(parent)
    , m_queueManager(queueManager)
    , m_dataFilePath("data/queue_data.txt")
    , m_backupFilePath("data/backup/")
    , m_autoSaveEnabled(true)
    , m_autoSaveInterval(30000) // 30秒自动保存
    , m_autoSaveTimer(new QTimer(this))
    , m_dailyCheckTimer(new QTimer(this))
    , m_lastSaveDate(QDate::currentDate())
    , m_dataChanged(false)
{
    initTimers();
    
    // 确保数据目录存在
    QDir dataDir;
    if (!dataDir.exists("data")) {
        dataDir.mkdir("data");
    }
    if (!dataDir.exists("data/backup")) {
        dataDir.mkpath("data/backup");
    }
    
    // 连接队列管理器信号
    if (m_queueManager) {
        connect(m_queueManager, SIGNAL(queueChanged()), this, SLOT(onQueueChanged()));
    }
}

DataManager::~DataManager()
{
    stop();
    
    // 最后保存一次数据
    if (m_dataChanged) {
        saveData();
    }
}

void DataManager::setDataFilePath(const QString& filePath)
{
    m_dataFilePath = filePath;
}

QString DataManager::getDataFilePath() const
{
    return m_dataFilePath;
}

void DataManager::setBackupFilePath(const QString& filePath)
{
    m_backupFilePath = filePath;
}

QString DataManager::getBackupFilePath() const
{
    return m_backupFilePath;
}

void DataManager::setAutoSaveEnabled(bool enabled)
{
    m_autoSaveEnabled = enabled;
    
    if (enabled && !m_autoSaveTimer->isActive()) {
        m_autoSaveTimer->start();
    } else if (!enabled && m_autoSaveTimer->isActive()) {
        m_autoSaveTimer->stop();
    }
}

bool DataManager::isAutoSaveEnabled() const
{
    return m_autoSaveEnabled;
}

void DataManager::setAutoSaveInterval(int intervalMs)
{
    m_autoSaveInterval = intervalMs;
    m_autoSaveTimer->setInterval(intervalMs);
}

int DataManager::getAutoSaveInterval() const
{
    return m_autoSaveInterval;
}

bool DataManager::saveData()
{
    if (!m_queueManager) {
        emit saveError(QString::fromUtf8("队列管理器未设置"));
        return false;
    }
    
    bool success = m_queueManager->saveToFile(m_dataFilePath);
    
    if (success) {
        m_dataChanged = false;
        updateLastSaveDate();
        emit dataSaved();
        qDebug() << "Data saved successfully to" << m_dataFilePath;
    } else {
        emit saveError(QString::fromUtf8("保存数据失败"));
        qDebug() << "Failed to save data to" << m_dataFilePath;
    }
    
    return success;
}

bool DataManager::loadData()
{
    if (!m_queueManager) {
        emit loadError(QString::fromUtf8("队列管理器未设置"));
        return false;
    }
    
    bool success = m_queueManager->loadFromFile(m_dataFilePath);
    
    if (success) {
        m_dataChanged = false;
        emit dataLoaded();
        qDebug() << "Data loaded successfully from" << m_dataFilePath;
    } else {
        emit loadError(QString::fromUtf8("加载数据失败"));
        qDebug() << "Failed to load data from" << m_dataFilePath;
    }
    
    return success;
}

bool DataManager::backupData()
{
    QString backupFileName = generateBackupFileName();
    QString backupFullPath = m_backupFilePath + backupFileName;
    
    bool success = copyFile(m_dataFilePath, backupFullPath);
    
    if (success) {
        emit dataBackedUp();
        qDebug() << "Data backed up to" << backupFullPath;
    } else {
        qDebug() << "Failed to backup data to" << backupFullPath;
    }
    
    return success;
}

bool DataManager::restoreFromBackup()
{
    // 查找最新的备份文件
    QDir backupDir(m_backupFilePath);
    QStringList backupFiles = backupDir.entryList(QStringList() << "queue_data_*.txt", QDir::Files, QDir::Time);
    
    if (backupFiles.isEmpty()) {
        qDebug() << "No backup files found";
        return false;
    }
    
    QString latestBackup = m_backupFilePath + backupFiles.first();
    bool success = copyFile(latestBackup, m_dataFilePath);
    
    if (success) {
        // 重新加载数据
        loadData();
        emit dataRestored();
        qDebug() << "Data restored from" << latestBackup;
    } else {
        qDebug() << "Failed to restore data from" << latestBackup;
    }
    
    return success;
}

bool DataManager::isNewDay() const
{
    return QDate::currentDate() != m_lastSaveDate;
}

void DataManager::checkAndResetDaily()
{
    if (isNewDay()) {
        qDebug() << "New day detected, resetting daily data";
        
        // 先备份当前数据
        backupData();
        
        // 重置队列管理器的日期数据
        if (m_queueManager) {
            m_queueManager->resetDailyNumbers();
            m_queueManager->updateLastDate();
        }
        
        // 保存重置后的数据
        saveData();
        
        emit dailyReset();
    }
}

void DataManager::clearAllData()
{
    if (m_queueManager) {
        m_queueManager->clearQueue();
        m_queueManager->resetDailyNumbers();
        saveData();
    }
}

void DataManager::clearExpiredData()
{
    // 这里可以添加清理过期数据的逻辑
    // 例如清理过期的票据记录等
    if (m_queueManager) {
        // 目前队列管理器会自动处理过期票据
        // 这里可以添加额外的清理逻辑
        saveData();
    }
}

void DataManager::start()
{
    // 启动时先加载数据
    loadData();
    
    // 检查是否需要日期重置
    checkAndResetDaily();
    
    // 启动定时器
    if (m_autoSaveEnabled) {
        m_autoSaveTimer->start();
    }
    
    m_dailyCheckTimer->start();
}

void DataManager::stop()
{
    m_autoSaveTimer->stop();
    m_dailyCheckTimer->stop();
    
    // 停止时保存数据
    if (m_dataChanged) {
        saveData();
    }
}

void DataManager::onAutoSave()
{
    if (m_dataChanged) {
        saveData();
    }
}

void DataManager::onDailyCheck()
{
    checkAndResetDaily();
}

void DataManager::onQueueChanged()
{
    m_dataChanged = true;
}

void DataManager::initTimers()
{
    // 自动保存定时器
    m_autoSaveTimer->setInterval(m_autoSaveInterval);
    m_autoSaveTimer->setSingleShot(false);
    connect(m_autoSaveTimer, SIGNAL(timeout()), this, SLOT(onAutoSave()));
    
    // 日期检查定时器 - 每小时检查一次
    m_dailyCheckTimer->setInterval(3600000); // 1小时
    m_dailyCheckTimer->setSingleShot(false);
    connect(m_dailyCheckTimer, SIGNAL(timeout()), this, SLOT(onDailyCheck()));
}

void DataManager::updateLastSaveDate()
{
    m_lastSaveDate = QDate::currentDate();
}

QString DataManager::generateBackupFileName() const
{
    QDateTime now = QDateTime::currentDateTime();
    return QString("queue_data_%1.txt").arg(now.toString("yyyyMMdd_hhmmss"));
}

bool DataManager::copyFile(const QString& source, const QString& destination)
{
    QFile sourceFile(source);
    if (!sourceFile.exists()) {
        return false;
    }
    
    // 如果目标文件存在，先删除
    QFile destFile(destination);
    if (destFile.exists()) {
        destFile.remove();
    }
    
    return sourceFile.copy(destination);
}
