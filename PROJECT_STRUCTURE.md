# 银行取号叫号系统 - 项目结构

## 目录结构

```
bank_queue_system/
├── README.md                           # 项目说明文档
├── PROJECT_STRUCTURE.md               # 项目结构说明（本文件）
├── config.ini                         # 系统配置文件
├── bank_queue_system.pro              # Qt主项目文件
├── CMakeLists.txt                      # CMake配置文件
├── build.sh                           # 编译脚本（Linux）
├── test_system.sh                     # 测试脚本（Linux）
│
├── common/                             # 公共库目录
│   ├── common.pro                      # 公共库项目文件
│   ├── ticket.h                        # 票据类头文件
│   ├── ticket.cpp                      # 票据类实现
│   ├── queue_manager.h                 # 队列管理器头文件
│   ├── queue_manager.cpp               # 队列管理器实现
│   ├── message_protocol.h              # 消息协议头文件
│   ├── message_protocol.cpp            # 消息协议实现
│   ├── network_client.h                # 网络客户端头文件
│   ├── network_client.cpp              # 网络客户端实现
│   ├── data_manager.h                  # 数据管理器头文件
│   └── data_manager.cpp                # 数据管理器实现
│
├── ticket_machine/                     # 取号机目录
│   ├── ticket_machine.pro              # 取号机项目文件
│   ├── main.cpp                        # 取号机主程序
│   ├── ticket_machine_window.h         # 取号机窗口头文件
│   └── ticket_machine_window.cpp       # 取号机窗口实现
│
├── calling_machine/                    # 叫号机目录
│   ├── calling_machine.pro             # 叫号机项目文件
│   ├── main.cpp                        # 叫号机主程序
│   ├── calling_machine_window.h        # 叫号机窗口头文件
│   └── calling_machine_window.cpp      # 叫号机窗口实现
│
├── bin/                                # 可执行文件目录（编译后生成）
│   ├── ticket_machine                  # 取号机可执行文件
│   ├── calling_machine                 # 叫号机可执行文件
│   └── data/                           # 数据目录
│       ├── queue_data.txt              # 队列数据文件
│       └── backup/                     # 备份目录
│           └── queue_data_*.txt        # 备份文件
│
├── lib/                                # 库文件目录（编译后生成）
│   └── libcommon.a                     # 公共静态库
│
└── build/                              # 编译临时文件目录
    ├── common/                         # 公共库编译文件
    ├── ticket_machine/                 # 取号机编译文件
    └── calling_machine/                # 叫号机编译文件
```

## 核心模块说明

### 1. 公共库 (common/)

#### ticket.h/cpp - 票据类
- **功能**: 定义票据数据结构和操作
- **特性**: 
  - 支持号码格式验证（如"VA001"）
  - 用户类型管理（普通/VIP）
  - 业务类型管理（储蓄/咨询）
  - 票据状态跟踪
  - 序列化/反序列化

#### queue_manager.h/cpp - 队列管理器
- **功能**: 管理取号队列和叫号逻辑
- **特性**:
  - VIP优先级队列
  - 自动号码生成
  - 重复呼叫控制（最多3次）
  - 票据过期处理
  - 数据持久化支持

#### message_protocol.h/cpp - 消息协议
- **功能**: 定义网络通信消息格式
- **特性**:
  - 统一消息编码/解码
  - 支持多种消息类型
  - 参数验证和错误处理
  - 广播消息支持

#### network_client.h/cpp - 网络客户端
- **功能**: TCP网络通信管理
- **特性**:
  - 自动重连机制
  - 心跳保活
  - 消息队列管理
  - 异步通信支持

#### data_manager.h/cpp - 数据管理器
- **功能**: 数据持久化和备份管理
- **特性**:
  - 自动保存机制
  - 断电恢复支持
  - 每日数据重置
  - 备份文件管理

### 2. 取号机 (ticket_machine/)

#### ticket_machine_window.h/cpp - 取号机主窗口
- **功能**: 取号机用户界面和业务逻辑
- **特性**:
  - 四种业务类型按钮
  - 实时队列状态显示
  - 最近取号记录
  - 当前叫号信息显示
  - 网络状态监控

### 3. 叫号机 (calling_machine/)

#### calling_machine_window.h/cpp - 叫号机主窗口
- **功能**: 叫号机用户界面和业务逻辑
- **特性**:
  - 叫号控制按钮
  - LCD数字显示
  - 队列状态统计
  - 操作记录日志
  - 窗口状态管理

## 编译配置

### Qt项目文件 (.pro)
- **bank_queue_system.pro**: 主项目文件，管理子项目依赖
- **common.pro**: 公共库配置，生成静态库
- **ticket_machine.pro**: 取号机应用配置
- **calling_machine.pro**: 叫号机应用配置

### CMake配置
- **CMakeLists.txt**: 跨平台编译配置，支持Qt4查找和链接

## 数据流程

### 取号流程
1. 用户点击取号按钮
2. 生成新票据（本地或网络）
3. 更新队列状态
4. 广播新增票据消息
5. 保存数据到文件

### 叫号流程
1. 点击"叫下一号"按钮
2. 从队列获取下一票据
3. 更新当前处理状态
4. 广播叫号消息
5. 启动超时定时器

### 网络同步
1. 客户端注册到网络
2. 发送业务请求消息
3. 接收响应和广播消息
4. 更新本地状态
5. 心跳保活连接

## 配置说明

### config.ini 配置项
- **Network**: 网络连接参数
- **Data**: 数据存储配置
- **Queue**: 队列管理参数
- **UI**: 界面更新设置
- **System**: 系统运行参数

## 部署说明

### 开发环境
1. Ubuntu 18.04 + Qt 4.8.5
2. 运行 `./build.sh` 编译
3. 运行 `./test_system.sh` 测试

### 生产环境
1. 将编译好的 `bin/` 目录复制到目标设备
2. 确保 `data/` 目录存在且可写
3. 配置网络地址（修改源码或配置文件）
4. 启动相应的客户端程序

## 扩展指南

### 添加新业务类型
1. 修改 `Ticket::BusinessType` 枚举
2. 更新相关显示文本函数
3. 调整界面按钮布局
4. 更新消息协议支持

### 添加新功能模块
1. 在 `common/` 中添加新的类文件
2. 更新 `common.pro` 和 `CMakeLists.txt`
3. 在客户端中集成新功能
4. 更新网络协议（如需要）

### 界面定制
1. 修改窗口类的 `initUI()` 函数
2. 调整样式表和布局
3. 添加新的信号槽连接
4. 更新显示更新逻辑
