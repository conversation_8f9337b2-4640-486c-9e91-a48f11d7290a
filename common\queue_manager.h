#ifndef QUEUE_MANAGER_H
#define QUEUE_MANAGER_H

#include "ticket.h"
#include <QObject>
#include <QList>
#include <QMutex>
#include <QTimer>
#include <QDate>

/**
 * 队列管理器
 * 管理取号队列，支持VIP优先级和数据持久化
 */
class QueueManager : public QObject
{
    Q_OBJECT

public:
    explicit QueueManager(QObject *parent = 0);
    ~QueueManager();
    
    // 取号操作
    Ticket takeNumber(Ticket::UserType userType, Ticket::BusinessType businessType);
    
    // 叫号操作
    Ticket callNext();
    bool callCurrent();  // 重复呼叫当前号码
    void finishCurrent(); // 完成当前号码
    void expireCurrent();  // 当前号码过期
    
    // 队列查询
    int getWaitingCount() const;
    int getWaitingCount(Ticket::UserType userType, Ticket::BusinessType businessType) const;
    QList<Ticket> getWaitingTickets() const;
    Ticket getCurrentTicket() const;
    bool hasCurrentTicket() const;
    
    // 号码管理
    int getNextNumber(Ticket::UserType userType, Ticket::BusinessType businessType);
    void resetDailyNumbers(); // 重置每日号码
    
    // 数据持久化
    bool saveToFile(const QString& filename);
    bool loadFromFile(const QString& filename);
    void clearQueue();
    
    // 日期检查
    bool isNewDay() const;
    void updateLastDate();

signals:
    void ticketAdded(const Ticket& ticket);
    void ticketCalled(const Ticket& ticket);
    void ticketFinished(const Ticket& ticket);
    void ticketExpired(const Ticket& ticket);
    void queueChanged();

private slots:
    void checkExpiredTickets();

private:
    QList<Ticket> m_waitingQueue;  // 等待队列
    Ticket m_currentTicket;        // 当前叫号
    bool m_hasCurrentTicket;       // 是否有当前号码
    
    // 号码计数器
    int m_normalSavingsCount;      // CA系列计数
    int m_normalConsultingCount;   // CB系列计数
    int m_vipSavingsCount;         // VA系列计数
    int m_vipConsultingCount;      // VB系列计数
    
    QDate m_lastDate;              // 上次使用日期
    QMutex m_mutex;                // 线程安全
    QTimer* m_expireTimer;         // 过期检查定时器
    
    void insertTicketByPriority(const Ticket& ticket);
    void removeTicketFromQueue(const Ticket& ticket);
    int& getCounterRef(Ticket::UserType userType, Ticket::BusinessType businessType);
    void initCounters();
    void startExpireTimer();
    void stopExpireTimer();
};

#endif // QUEUE_MANAGER_H
