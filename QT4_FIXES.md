# Qt 4.8.5 兼容性修复说明

本文档记录了为确保代码与Qt 4.8.5兼容而进行的修复。

## 修复的问题

### 1. QStringList 操作符问题

**问题**: Qt 4.8.5 中 QStringList 不支持 `<<` 操作符和 `[]` 数组访问操作符

**修复前**:
```cpp
QStringList params;
params << "value1" << "value2";
QString first = params[0];
```

**修复后**:
```cpp
QStringList params;
params.append("value1");
params.append("value2");
QString first = params.at(0);
```

**影响的文件**:
- `common/message_protocol.cpp` - 所有消息构建和解析函数
- `common/ticket.cpp` - toString() 和 fromString() 函数
- `common/queue_manager.cpp` - 数据加载函数

### 2. QString 字符访问问题

**问题**: Qt 4.8.5 中 QString 不支持 `[]` 数组访问操作符

**修复前**:
```cpp
QString str = "ABC";
QChar first = str[0];
```

**修复后**:
```cpp
QString str = "ABC";
QChar first = str.at(0);
```

**影响的文件**:
- `common/ticket.cpp` - 票据号码解析函数

### 3. QList 操作符问题

**问题**: Qt 4.8.5 中 QList 不支持 `<<` 操作符

**修复前**:
```cpp
QList<QPushButton*> buttons;
buttons << btn1 << btn2 << btn3;
```

**修复后**:
```cpp
QList<QPushButton*> buttons;
buttons.append(btn1);
buttons.append(btn2);
buttons.append(btn3);
```

**影响的文件**:
- `ticket_machine/ticket_machine_window.cpp` - 按钮列表初始化
- `calling_machine/calling_machine_window.cpp` - 按钮列表初始化

### 4. QString 隐式转换问题

**问题**: Qt 4.8.5 中 QString 与 const char* 之间的隐式转换更严格

**修复前**:
```cpp
QString result = list.join(",");
QStringList parts = str.split("|");
QString text = lines.join('\n');
```

**修复后**:
```cpp
QString result = list.join(QString(","));
QStringList parts = str.split(QString("|"));
QString text = lines.join(QString("\n"));
```

**影响的文件**:
- `common/message_protocol.cpp` - 所有消息解析函数
- `common/ticket.cpp` - 字符串序列化函数
- `common/queue_manager.cpp` - 数据解析函数
- `calling_machine/calling_machine_window.cpp` - 日志显示函数

## 编译说明

### 推荐编译方法

使用专门的Qt 4.8.5编译脚本：
```bash
chmod +x build_qt4.sh
./build_qt4.sh
```

### 手动编译方法

```bash
# 清理
make clean
rm -f Makefile

# 生成Makefile
qmake bank_queue_system.pro

# 编译
make -j$(nproc)
```

### 验证编译结果

编译成功后应该生成以下文件：
- `bin/ticket_machine` - 取号机可执行文件
- `bin/calling_machine` - 叫号机可执行文件  
- `lib/libcommon.a` - 公共静态库

## 运行时注意事项

### 1. 中文编码支持

确保在main函数中设置正确的编码：
```cpp
QTextCodec::setCodecForTr(QTextCodec::codecForName("UTF-8"));
QTextCodec::setCodecForCStrings(QTextCodec::codecForName("UTF-8"));
QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
```

### 2. 信号槽连接

使用Qt 4.8.5兼容的SIGNAL/SLOT宏：
```cpp
connect(sender, SIGNAL(signalName(Type)), receiver, SLOT(slotName(Type)));
```

### 3. 网络功能

确保链接了network模块：
```pro
QT += network
```

## 测试验证

### 基本功能测试

1. **编译测试**:
   ```bash
   ./build_qt4.sh
   ```

2. **运行测试**:
   ```bash
   ./test_system.sh
   ```

3. **功能测试**:
   - 启动取号机，测试四种业务类型取号
   - 启动叫号机，测试叫号、重复呼叫、完成办理功能
   - 测试网络连接和数据同步

### 兼容性验证

在Ubuntu 18.04 + Qt 4.8.5环境下验证：
- 所有编译警告已消除
- 运行时无崩溃
- 中文界面显示正常
- 网络通信正常

## 已知限制

### Qt 4.8.5 限制

1. **C++11特性**: 避免使用auto、lambda、范围for等C++11特性
2. **容器操作**: 使用传统的append()、at()方法而非现代操作符
3. **字符串操作**: 使用at()方法访问字符，避免[]操作符

### 功能限制

1. **语音播报**: 暂未实现，需要额外的音频库支持
2. **网络服务器**: 当前只实现客户端，需要独立的服务器程序
3. **数据库**: 使用文件存储，未集成数据库

## 后续改进建议

### 短期改进

1. 添加更多的错误处理和日志记录
2. 优化界面布局适配不同屏幕尺寸
3. 添加配置文件支持

### 长期改进

1. 升级到Qt 5.x版本以获得更好的性能和功能
2. 添加数据库支持
3. 实现完整的服务器端程序
4. 添加语音播报功能

## 故障排除

### 编译错误

1. **找不到Qt**: 确保安装了qt4-dev-tools
2. **链接错误**: 检查库文件路径和依赖关系
3. **MOC错误**: 清理后重新编译

### 运行错误

1. **中文乱码**: 检查编码设置
2. **网络连接失败**: 检查防火墙和端口设置
3. **文件权限**: 确保data目录可写

## 联系支持

如遇到问题，请检查：
1. Qt版本是否为4.8.5
2. 编译环境是否正确配置
3. 是否按照本文档进行了必要的修复
