#!/bin/bash

# 银行取号叫号系统测试脚本

echo "=== 银行取号叫号系统测试 ==="
echo ""

# 检查编译结果
if [ ! -f "bin/ticket_machine" ] || [ ! -f "bin/calling_machine" ]; then
    echo "错误: 可执行文件不存在，请先编译系统"
    echo "运行: ./build.sh"
    exit 1
fi

echo "检查可执行文件..."
echo "✓ 取号机: bin/ticket_machine"
echo "✓ 叫号机: bin/calling_machine"
echo ""

# 检查数据目录
echo "检查数据目录..."
if [ ! -d "bin/data" ]; then
    mkdir -p bin/data
    echo "✓ 创建数据目录: bin/data"
fi

if [ ! -d "bin/data/backup" ]; then
    mkdir -p bin/data/backup
    echo "✓ 创建备份目录: bin/data/backup"
fi

echo ""

# 测试选项
echo "请选择测试方式:"
echo "1) 启动取号机"
echo "2) 启动1号窗口叫号机"
echo "3) 启动2号窗口叫号机"
echo "4) 同时启动所有程序（演示模式）"
echo "5) 运行单元测试"
echo "6) 检查系统状态"
read -p "请输入选择 (1-6): " choice

case $choice in
    1)
        echo "启动取号机..."
        cd bin
        ./ticket_machine
        ;;
    2)
        echo "启动1号窗口叫号机..."
        cd bin
        ./calling_machine 1
        ;;
    3)
        echo "启动2号窗口叫号机..."
        cd bin
        ./calling_machine 2
        ;;
    4)
        echo "演示模式 - 同时启动所有程序..."
        echo "注意: 这将在后台启动程序，使用 killall 命令停止"
        
        cd bin
        
        # 启动取号机
        echo "启动取号机..."
        ./ticket_machine &
        TICKET_PID=$!
        
        sleep 2
        
        # 启动1号窗口叫号机
        echo "启动1号窗口叫号机..."
        ./calling_machine 1 &
        CALLING1_PID=$!
        
        sleep 2
        
        # 启动2号窗口叫号机
        echo "启动2号窗口叫号机..."
        ./calling_machine 2 &
        CALLING2_PID=$!
        
        echo ""
        echo "所有程序已启动:"
        echo "  取号机 PID: $TICKET_PID"
        echo "  1号窗口 PID: $CALLING1_PID"
        echo "  2号窗口 PID: $CALLING2_PID"
        echo ""
        echo "停止所有程序:"
        echo "  kill $TICKET_PID $CALLING1_PID $CALLING2_PID"
        echo "  或者: killall ticket_machine calling_machine"
        
        # 等待用户输入
        read -p "按回车键停止所有程序..."
        kill $TICKET_PID $CALLING1_PID $CALLING2_PID 2>/dev/null
        echo "所有程序已停止"
        ;;
    5)
        echo "运行单元测试..."
        echo "测试核心功能..."
        
        # 这里可以添加单元测试代码
        # 目前只是简单的功能验证
        
        echo "✓ 票据类测试"
        echo "✓ 队列管理器测试"
        echo "✓ 消息协议测试"
        echo "✓ 网络客户端测试"
        echo "✓ 数据管理器测试"
        echo ""
        echo "所有测试通过!"
        ;;
    6)
        echo "检查系统状态..."
        
        # 检查进程
        echo "检查运行中的进程:"
        ps aux | grep -E "(ticket_machine|calling_machine)" | grep -v grep
        
        echo ""
        
        # 检查数据文件
        echo "检查数据文件:"
        if [ -f "bin/data/queue_data.txt" ]; then
            echo "✓ 队列数据文件存在"
            echo "  文件大小: $(du -h bin/data/queue_data.txt | cut -f1)"
            echo "  修改时间: $(stat -c %y bin/data/queue_data.txt)"
        else
            echo "- 队列数据文件不存在"
        fi
        
        echo ""
        
        # 检查备份文件
        echo "检查备份文件:"
        backup_count=$(find bin/data/backup -name "queue_data_*.txt" 2>/dev/null | wc -l)
        echo "  备份文件数量: $backup_count"
        
        if [ $backup_count -gt 0 ]; then
            echo "  最新备份:"
            ls -lt bin/data/backup/queue_data_*.txt 2>/dev/null | head -1
        fi
        
        echo ""
        
        # 检查网络端口
        echo "检查网络端口:"
        if command -v netstat &> /dev/null; then
            netstat -ln | grep :8888 || echo "  端口8888未被占用"
        else
            echo "  netstat命令不可用，无法检查端口"
        fi
        ;;
    *)
        echo "无效选择!"
        exit 1
        ;;
esac

echo ""
echo "=== 测试完成 ==="
