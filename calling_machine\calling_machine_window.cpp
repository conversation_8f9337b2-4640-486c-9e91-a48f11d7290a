#include "calling_machine_window.h"
#include <QApplication>
#include <QMessageBox>
#include <QDateTime>
#include <QFont>
#include <QSplitter>
#include <QScrollBar>
#include <QHBoxLayout>
#include <QDebug>

CallingMachineWindow::CallingMachineWindow(int windowId, QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(0)
    , m_networkClient(0)
    , m_queueManager(0)
    , m_displayTimer(0)
    , m_statusTimer(0)
    , m_callTimer(0)
    , m_windowId(windowId)
    , m_networkConnected(false)
    , m_hasCurrentTicket(false)
    , m_callCount(0)
    , m_totalWaiting(0)
    , m_normalSavingsWaiting(0)
    , m_normalConsultingWaiting(0)
    , m_vipSavingsWaiting(0)
    , m_vipConsultingWaiting(0)
{
    qDebug() << "CallingMachineWindow constructor started for window" << windowId;

    setWindowTitle(QString("Bank Calling System - Window %1").arg(m_windowId));
    setMinimumSize(1000, 700);

    qDebug() << "Starting initUI...";
    initUI();
    qDebug() << "initUI completed";

    qDebug() << "Starting initNetwork...";
    initNetwork();
    qDebug() << "initNetwork completed";

    qDebug() << "Starting initTimers...";
    initTimers();
    qDebug() << "initTimers completed";

    qDebug() << "Starting setupConnections...";
    setupConnections();
    qDebug() << "setupConnections completed";

    // 加载本地数据
    qDebug() << "Loading local data...";
    bool loadResult = m_queueManager->loadFromFile("data/queue_data.txt");
    qDebug() << "Local data load result:" << loadResult;

    // 尝试连接到服务器（失败时自动切换到离线模式）
    qDebug() << "Connecting to server...";
    connectToServer();
    qDebug() << "Server connection initiated";

    // 初始化界面状态
    qDebug() << "Updating UI state...";
    updateCurrentTicket();
    updateWaitingCounts();
    updateQueueDisplay();
    updateTimeDisplay();
    qDebug() << "CallingMachineWindow constructor completed";
}

CallingMachineWindow::~CallingMachineWindow()
{
    if (m_networkClient) {
        m_networkClient->disconnectFromHost();
    }
}

void CallingMachineWindow::initUI()
{
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_mainLayout->setSpacing(10);
    m_mainLayout->setMargin(10);
    
    // 标题区域
    m_titleLabel = new QLabel("Bank Calling System", this);
    QFont titleFont = m_titleLabel->font();
    titleFont.setPointSize(24);
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_titleLabel->setStyleSheet("QLabel { color: #2c3e50; padding: 10px; }");

    m_windowLabel = new QLabel(QString("Window %1").arg(m_windowId), this);
    QFont windowFont = m_windowLabel->font();
    windowFont.setPointSize(18);
    windowFont.setBold(true);
    m_windowLabel->setFont(windowFont);
    m_windowLabel->setAlignment(Qt::AlignCenter);
    m_windowLabel->setStyleSheet("QLabel { color: #e74c3c; padding: 5px; }");
    
    m_timeLabel = new QLabel(this);
    QFont timeFont = m_timeLabel->font();
    timeFont.setPointSize(12);
    m_timeLabel->setFont(timeFont);
    m_timeLabel->setAlignment(Qt::AlignCenter);
    m_timeLabel->setStyleSheet("QLabel { color: #34495e; }");
    
    // 当前叫号显示区域
    m_currentGroup = new QGroupBox("Current Ticket", this);
    m_currentLayout = new QVBoxLayout(m_currentGroup);
    
    // LCD数字显示
    m_ticketDisplay = new QLCDNumber(5, this);
    m_ticketDisplay->setMinimumHeight(80);
    m_ticketDisplay->setStyleSheet(
        "QLCDNumber {"
        "    background-color: #2c3e50;"
        "    color: #e74c3c;"
        "    border: 2px solid #34495e;"
        "    border-radius: 5px;"
        "}"
    );
    m_ticketDisplay->display("-----");
    
    m_currentTicketLabel = new QLabel("No Current Ticket", this);
    m_currentStatusLabel = new QLabel("Window Available", this);
    m_callCountLabel = new QLabel("", this);
    
    QFont currentFont = m_currentTicketLabel->font();
    currentFont.setPointSize(16);
    currentFont.setBold(true);
    m_currentTicketLabel->setFont(currentFont);
    m_currentStatusLabel->setFont(currentFont);
    m_callCountLabel->setFont(currentFont);
    
    m_currentTicketLabel->setAlignment(Qt::AlignCenter);
    m_currentStatusLabel->setAlignment(Qt::AlignCenter);
    m_callCountLabel->setAlignment(Qt::AlignCenter);
    
    m_currentTicketLabel->setStyleSheet("QLabel { color: #2c3e50; }");
    m_currentStatusLabel->setStyleSheet("QLabel { color: #27ae60; }");
    m_callCountLabel->setStyleSheet("QLabel { color: #f39c12; }");
    
    m_currentLayout->addWidget(m_ticketDisplay);
    m_currentLayout->addWidget(m_currentTicketLabel);
    m_currentLayout->addWidget(m_currentStatusLabel);
    m_currentLayout->addWidget(m_callCountLabel);
    
    // 叫号控制按钮区域
    m_controlGroup = new QGroupBox("Call Control", this);
    m_controlLayout = new QGridLayout(m_controlGroup);
    m_controlLayout->setSpacing(15);
    
    m_callNextBtn = new QPushButton("Call Next\n(Green)", this);
    m_callCurrentBtn = new QPushButton("Repeat Call\n(Orange)", this);
    m_finishCurrentBtn = new QPushButton("Finish Service\n(Blue)", this);
    m_resetBtn = new QPushButton("Reset Window\n(Red)", this);
    
    // 设置按钮样式
    QList<QPushButton*> buttons;
    buttons.append(m_callNextBtn);
    buttons.append(m_callCurrentBtn);
    buttons.append(m_finishCurrentBtn);
    buttons.append(m_resetBtn);
    
    foreach (QPushButton* btn, buttons) {
        btn->setMinimumSize(140, 80);
        btn->setFont(QFont("Microsoft YaHei", 11, QFont::Bold));
    }
    
    // 叫下一号按钮 - 绿色
    m_callNextBtn->setStyleSheet(
        "QPushButton {"
        "    background-color: #27ae60;"
        "    color: white;"
        "    border: none;"
        "    border-radius: 8px;"
        "    padding: 10px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #229954;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #1e8449;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #bdc3c7;"
        "    color: #7f8c8d;"
        "}"
    );
    
    // 重复呼叫按钮 - 橙色
    m_callCurrentBtn->setStyleSheet(
        "QPushButton {"
        "    background-color: #f39c12;"
        "    color: white;"
        "    border: none;"
        "    border-radius: 8px;"
        "    padding: 10px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #e67e22;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #d35400;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #bdc3c7;"
        "    color: #7f8c8d;"
        "}"
    );
    
    // 完成办理按钮 - 蓝色
    m_finishCurrentBtn->setStyleSheet(
        "QPushButton {"
        "    background-color: #3498db;"
        "    color: white;"
        "    border: none;"
        "    border-radius: 8px;"
        "    padding: 10px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #2980b9;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #21618c;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #bdc3c7;"
        "    color: #7f8c8d;"
        "}"
    );
    
    // 重置窗口按钮 - 红色
    m_resetBtn->setStyleSheet(
        "QPushButton {"
        "    background-color: #e74c3c;"
        "    color: white;"
        "    border: none;"
        "    border-radius: 8px;"
        "    padding: 10px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #c0392b;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #a93226;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #bdc3c7;"
        "    color: #7f8c8d;"
        "}"
    );
    
    // 添加按钮功能说明
    QLabel* callNextDesc = new QLabel("Call next number from queue", this);
    QLabel* callCurrentDesc = new QLabel("Repeat current number call", this);
    QLabel* finishDesc = new QLabel("Complete current service", this);
    QLabel* resetDesc = new QLabel("Reset window status", this);

    callNextDesc->setStyleSheet("QLabel { color: #7f8c8d; font-size: 10px; }");
    callCurrentDesc->setStyleSheet("QLabel { color: #7f8c8d; font-size: 10px; }");
    finishDesc->setStyleSheet("QLabel { color: #7f8c8d; font-size: 10px; }");
    resetDesc->setStyleSheet("QLabel { color: #7f8c8d; font-size: 10px; }");

    m_controlLayout->addWidget(m_callNextBtn, 0, 0);
    m_controlLayout->addWidget(callNextDesc, 1, 0);
    m_controlLayout->addWidget(m_callCurrentBtn, 0, 1);
    m_controlLayout->addWidget(callCurrentDesc, 1, 1);
    m_controlLayout->addWidget(m_finishCurrentBtn, 2, 0);
    m_controlLayout->addWidget(finishDesc, 3, 0);
    m_controlLayout->addWidget(m_resetBtn, 2, 1);
    m_controlLayout->addWidget(resetDesc, 3, 1);
    
    // 状态显示区域
    m_statusGroup = new QGroupBox("Queue Status", this);
    m_statusLayout = new QVBoxLayout(m_statusGroup);
    
    m_connectionStatusLabel = new QLabel("Connection Status: Disconnected", this);
    m_totalWaitingLabel = new QLabel("Total Waiting: 0", this);
    m_normalSavingsWaitingLabel = new QLabel("Normal Savings (CA) Waiting: 0", this);
    m_normalConsultingWaitingLabel = new QLabel("Normal Consulting (CB) Waiting: 0", this);
    m_vipSavingsWaitingLabel = new QLabel("VIP Savings (VA) Waiting: 0", this);
    m_vipConsultingWaitingLabel = new QLabel("VIP Consulting (VB) Waiting: 0", this);
    
    m_statusLayout->addWidget(m_connectionStatusLabel);
    m_statusLayout->addWidget(m_totalWaitingLabel);
    m_statusLayout->addWidget(m_normalSavingsWaitingLabel);
    m_statusLayout->addWidget(m_normalConsultingWaitingLabel);
    m_statusLayout->addWidget(m_vipSavingsWaitingLabel);
    m_statusLayout->addWidget(m_vipConsultingWaitingLabel);
    
    // 队列显示区域
    m_queueGroup = new QGroupBox("Waiting Queue", this);
    QVBoxLayout* queueLayout = new QVBoxLayout(m_queueGroup);

    m_queueText = new QTextEdit(this);
    m_queueText->setReadOnly(true);
    m_queueText->setMaximumHeight(150);
    m_queueText->setFont(QFont("Consolas", 10));

    queueLayout->addWidget(m_queueText);

    // 操作记录区域
    m_logGroup = new QGroupBox("Operation Log", this);
    QVBoxLayout* logLayout = new QVBoxLayout(m_logGroup);
    
    m_logText = new QTextEdit(this);
    m_logText->setReadOnly(true);
    m_logText->setMaximumHeight(120);
    m_logText->setFont(QFont("Consolas", 9));
    
    logLayout->addWidget(m_logText);
    
    // 布局组装
    m_mainLayout->addWidget(m_titleLabel);
    m_mainLayout->addWidget(m_windowLabel);
    m_mainLayout->addWidget(m_timeLabel);
    
    // 创建水平分割器
    QSplitter* topSplitter = new QSplitter(Qt::Horizontal, this);
    topSplitter->addWidget(m_currentGroup);
    topSplitter->addWidget(m_controlGroup);
    topSplitter->setStretchFactor(0, 2);
    topSplitter->setStretchFactor(1, 1);
    
    QSplitter* middleSplitter = new QSplitter(Qt::Horizontal, this);
    middleSplitter->addWidget(m_statusGroup);
    middleSplitter->addWidget(m_queueGroup);
    middleSplitter->setStretchFactor(0, 1);
    middleSplitter->setStretchFactor(1, 2);
    
    m_mainLayout->addWidget(topSplitter);
    m_mainLayout->addWidget(middleSplitter);
    m_mainLayout->addWidget(m_logGroup);
    
    // 设置布局比例
    m_mainLayout->setStretchFactor(topSplitter, 3);
    m_mainLayout->setStretchFactor(middleSplitter, 2);
    m_mainLayout->setStretchFactor(m_logGroup, 1);
}

void CallingMachineWindow::initNetwork()
{
    m_networkClient = new NetworkClient(this);
    m_networkClient->setClientInfo(MessageProtocol::CALLING_MACHINE, m_windowId,
                                   QString("CallingMachine-%1").arg(m_windowId));

    m_queueManager = new QueueManager(this);

    // 尝试加载持久化数据
    m_queueManager->loadFromFile("data/queue_data.txt");
}

void CallingMachineWindow::initTimers()
{
    // 界面更新定时器 - 每秒更新一次
    m_displayTimer = new QTimer(this);
    m_displayTimer->setInterval(1000);
    m_displayTimer->start();

    // 状态查询定时器 - 每3秒查询一次（离线模式下重新加载数据文件）
    m_statusTimer = new QTimer(this);
    m_statusTimer->setInterval(3000);
    m_statusTimer->start();

    // 叫号超时定时器 - 10秒超时
    m_callTimer = new QTimer(this);
    m_callTimer->setInterval(10000);
    m_callTimer->setSingleShot(true);
}

void CallingMachineWindow::setupConnections()
{
    // 按钮连接
    connect(m_callNextBtn, SIGNAL(clicked()), this, SLOT(onCallNext()));
    connect(m_callCurrentBtn, SIGNAL(clicked()), this, SLOT(onCallCurrent()));
    connect(m_finishCurrentBtn, SIGNAL(clicked()), this, SLOT(onFinishCurrent()));
    connect(m_resetBtn, SIGNAL(clicked()), this, SLOT(onResetWindow()));

    // 网络连接
    connect(m_networkClient, SIGNAL(connected()), this, SLOT(onNetworkConnected()));
    connect(m_networkClient, SIGNAL(disconnected()), this, SLOT(onNetworkDisconnected()));
    connect(m_networkClient, SIGNAL(connectionError(QString)), this, SLOT(onNetworkError(QString)));
    connect(m_networkClient, SIGNAL(callNextResponse(Ticket,bool,QString)),
            this, SLOT(onCallNextResponse(Ticket,bool,QString)));
    connect(m_networkClient, SIGNAL(callCurrentResponse(bool,QString)),
            this, SLOT(onCallCurrentResponse(bool,QString)));
    connect(m_networkClient, SIGNAL(finishCurrentResponse(bool,QString)),
            this, SLOT(onFinishCurrentResponse(bool,QString)));
    connect(m_networkClient, SIGNAL(queueStatusResponse(int,QList<Ticket>)),
            this, SLOT(onQueueStatusResponse(int,QList<Ticket>)));

    // 广播消息连接
    connect(m_networkClient, SIGNAL(ticketAddedBroadcast(Ticket)),
            this, SLOT(onTicketAddedBroadcast(Ticket)));
    connect(m_networkClient, SIGNAL(ticketCalledBroadcast(Ticket,int)),
            this, SLOT(onTicketCalledBroadcast(Ticket,int)));
    connect(m_networkClient, SIGNAL(ticketFinishedBroadcast(Ticket,int)),
            this, SLOT(onTicketFinishedBroadcast(Ticket,int)));
    connect(m_networkClient, SIGNAL(ticketExpiredBroadcast(Ticket,int)),
            this, SLOT(onTicketExpiredBroadcast(Ticket,int)));
    connect(m_networkClient, SIGNAL(queueChangedBroadcast()),
            this, SLOT(onQueueChangedBroadcast()));

    // 定时器连接
    connect(m_displayTimer, SIGNAL(timeout()), this, SLOT(onUpdateDisplay()));
    connect(m_statusTimer, SIGNAL(timeout()), this, SLOT(onRequestQueueStatus()));
    connect(m_callTimer, SIGNAL(timeout()), this, SLOT(onCallTimeout()));

    // 暂时注释队列管理器信号连接，避免崩溃
    // connect(m_queueManager, SIGNAL(ticketCalled(Ticket)), this, SLOT(onLocalTicketCalled(Ticket)));
    // connect(m_queueManager, SIGNAL(ticketFinished(Ticket)), this, SLOT(onLocalTicketFinished(Ticket)));
    // connect(m_queueManager, SIGNAL(ticketExpired(Ticket)), this, SLOT(onLocalTicketExpired(Ticket)));
    // connect(m_queueManager, SIGNAL(queueChanged()), this, SLOT(onQueueChangedBroadcast()));
}

void CallingMachineWindow::connectToServer()
{
    // 尝试连接到服务器
    addLogEntry("Attempting to connect to server...");
    m_networkClient->connectToHost("127.0.0.1", 8889);

    // 设置5秒超时，如果连接失败则切换到离线模式
    QTimer::singleShot(5000, this, SLOT(onConnectionTimeout()));
}

void CallingMachineWindow::onCallNext()
{
    callNextTicket();
}

void CallingMachineWindow::onCallCurrent()
{
    callCurrentTicket();
}

void CallingMachineWindow::onFinishCurrent()
{
    finishCurrentTicket();
}

void CallingMachineWindow::onResetWindow()
{
    resetWindow();
}

void CallingMachineWindow::callNextTicket()
{
    qDebug() << "CallingMachineWindow::callNextTicket() started";
    // 统一使用本地队列管理器（混合模式）
    Ticket ticket = m_queueManager->callNext();
    qDebug() << "QueueManager::callNext() returned, ticket:" << ticket.getTicketNumber();
    if (!ticket.getTicketNumber().isEmpty()) {
        m_currentTicket = ticket;
        m_hasCurrentTicket = true;
        m_callCount = 1;  // 第一次叫号，设置为1
        m_lastCallTime = QDateTime::currentDateTime();

        // 手动更新界面显示（因为信号连接被注释了）
        updateCurrentTicket();
        updateButtonStates();
        updateTicketDisplay();
        updateWaitingCounts();  // 更新等待人数
        updateQueueDisplay();   // 更新队列显示

        addLogEntry(QString("Called: %1").arg(ticket.getDisplayText()));

        // 启动超时定时器
        m_callTimer->start();

        // 播放叫号声音
        playCallSound();

        // 保存数据
        qDebug() << "Saving queue data to file...";
        if (!m_queueManager->saveToFile("data/queue_data.txt")) {
            addLogEntry("Warning: Failed to save queue data to file");
        } else {
            qDebug() << "Queue data saved successfully";
        }

        // 如果网络连接，广播叫号消息
        if (m_networkConnected && m_networkClient->isConnected()) {
            addLogEntry("Broadcasting call to network");
            // 这里可以发送网络消息，但测试服务器不会处理
        }
        qDebug() << "CallingMachineWindow::callNextTicket() completed successfully";
    } else {
        addLogEntry("Queue is empty, cannot call next");
        showMessage("Notice", "Current queue is empty, cannot call next");
        qDebug() << "CallingMachineWindow::callNextTicket() - queue is empty";
    }
}

void CallingMachineWindow::callCurrentTicket()
{
    if (!m_hasCurrentTicket) {
        addLogEntry("No current ticket, cannot repeat call");
        showMessage("Notice", "No ticket currently being processed");
        return;
    }

    if (m_callCount >= 3) {
        addLogEntry("Maximum call attempts reached, ticket expired");
        showMessage("Notice", "This ticket has reached maximum call attempts and will expire");

        // 票据过期
        m_currentTicket.setStatus(Ticket::EXPIRED);
        m_hasCurrentTicket = false;
        m_callCount = 0;
        m_callTimer->stop();

        updateCurrentTicket();
        updateButtonStates();
        updateTicketDisplay();

        // 混合模式：不发送网络广播，使用本地队列管理器
        addLogEntry("Ticket expired due to maximum call attempts");

        return;
    }

    // 统一使用本地队列管理器（混合模式）
    if (m_queueManager->callCurrent()) {
        m_callCount++;
        m_lastCallTime = QDateTime::currentDateTime();

        // 手动更新界面显示
        updateCurrentTicket();
        updateButtonStates();

        addLogEntry(QString("Repeat call: %1 (attempt %2)").arg(m_currentTicket.getDisplayText()).arg(m_callCount));

        // 重启超时定时器
        m_callTimer->start();

        // 播放叫号声音
        playCallSound();

        // 如果网络连接，广播叫号消息
        if (m_networkConnected && m_networkClient->isConnected()) {
            addLogEntry("Broadcasting repeat call to network");
        }
    }
}

void CallingMachineWindow::finishCurrentTicket()
{
    if (!m_hasCurrentTicket) {
        addLogEntry("No current ticket, cannot finish service");
        showMessage("Notice", "No ticket currently being processed");
        return;
    }

    // 统一使用本地队列管理器（混合模式）
    m_queueManager->finishCurrent();

    addLogEntry(QString("Service completed: %1").arg(m_currentTicket.getDisplayText()));

    // 如果网络连接，广播完成消息
    if (m_networkConnected && m_networkClient->isConnected()) {
        addLogEntry("Broadcasting finish to network");
    }

    m_currentTicket = Ticket();
    m_hasCurrentTicket = false;
    m_callCount = 0;
    m_callTimer->stop();

    // 手动更新界面显示
    updateCurrentTicket();
    updateButtonStates();
    updateTicketDisplay();
    updateWaitingCounts();  // 更新等待人数
    updateQueueDisplay();   // 更新队列显示

    // 保存数据
    m_queueManager->saveToFile("data/queue_data.txt");
}

void CallingMachineWindow::resetWindow()
{
    if (m_hasCurrentTicket) {
        int ret = QMessageBox::question(this, "Confirm Reset",
                                        "There is a ticket currently being processed. Are you sure you want to reset the window?",
                                        QMessageBox::Yes | QMessageBox::No);
        if (ret != QMessageBox::Yes) {
            return;
        }

        // 如果有当前票据，先完成它
        finishCurrentTicket();
    }

    m_currentTicket = Ticket();
    m_hasCurrentTicket = false;
    m_callCount = 0;
    m_callTimer->stop();

    updateCurrentTicket();
    updateButtonStates();
    updateTicketDisplay();

    // 如果网络未连接，尝试重新连接
    if (!m_networkConnected) {
        addLogEntry("Window reset - Attempting to reconnect");
        connectToServer();
    } else {
        addLogEntry("Window reset");
    }
}

void CallingMachineWindow::connectToServer()
{
    // 尝试连接到服务器
    qDebug() << "Attempting to connect to server...";
    m_networkClient->connectToHost("127.0.0.1", 8889);

    // 设置5秒超时，如果连接失败则切换到离线模式
    QTimer::singleShot(5000, this, SLOT(onConnectionTimeout()));
}

void CallingMachineWindow::onNetworkConnected()
{
    m_networkConnected = true;
    updateConnectionStatus();
    updateButtonStates();

    addLogEntry("Network connected successfully");
    addLogEntry("Using hybrid mode - local queue with network connection");

    // 立即同步本地队列状态
    m_queueManager->loadFromFile("data/queue_data.txt");

    // 更新所有显示
    onRequestQueueStatus();
    updateTicketDisplay();
}

void CallingMachineWindow::onNetworkDisconnected()
{
    m_networkConnected = false;
    updateConnectionStatus();
    updateButtonStates();

    addLogEntry("Network disconnected");
}

void CallingMachineWindow::onNetworkError(const QString& error)
{
    m_networkConnected = false;
    updateConnectionStatus();
    updateButtonStates();

    addLogEntry(QString::fromUtf8("网络错误: %1 - 切换到离线模式").arg(error));

    // 不再自动重连，避免频繁错误信息
    // 用户可以手动点击重置按钮来重新连接
}

void CallingMachineWindow::onCallNextResponse(const Ticket& ticket, bool success, const QString& error)
{
    if (success) {
        m_currentTicket = ticket;
        m_hasCurrentTicket = true;
        m_callCount = ticket.getCallCount();
        m_lastCallTime = QDateTime::currentDateTime();

        updateCurrentTicket();
        updateButtonStates();
        updateTicketDisplay();

        addLogEntry(QString::fromUtf8("叫号成功: %1").arg(ticket.getDisplayText()));

        // 启动超时定时器
        m_callTimer->start();

        // 播放叫号声音
        playCallSound();
    } else {
        addLogEntry(QString::fromUtf8("叫号失败: %1").arg(error));
        showMessage(QString::fromUtf8("叫号失败"), error);
    }
}

void CallingMachineWindow::onCallCurrentResponse(bool success, const QString& error)
{
    if (success) {
        m_callCount++;
        m_lastCallTime = QDateTime::currentDateTime();

        updateCurrentTicket();

        addLogEntry(QString::fromUtf8("重复呼叫成功 (第%1次)").arg(m_callCount));

        // 重启超时定时器
        m_callTimer->start();

        // 播放叫号声音
        playCallSound();
    } else {
        addLogEntry(QString::fromUtf8("重复呼叫失败: %1").arg(error));
        showMessage(QString::fromUtf8("重复呼叫失败"), error);
    }
}

void CallingMachineWindow::onFinishCurrentResponse(bool success, const QString& error)
{
    if (success) {
        addLogEntry(QString::fromUtf8("完成办理成功: %1").arg(m_currentTicket.getDisplayText()));

        m_currentTicket = Ticket();
        m_hasCurrentTicket = false;
        m_callCount = 0;
        m_callTimer->stop();

        updateCurrentTicket();
        updateButtonStates();
        updateTicketDisplay();
    } else {
        addLogEntry(QString::fromUtf8("完成办理失败: %1").arg(error));
        showMessage(QString::fromUtf8("完成办理失败"), error);
    }
}

void CallingMachineWindow::onQueueStatusResponse(int waitingCount, const QList<Ticket>& tickets)
{
    m_totalWaiting = waitingCount;
    m_waitingTickets = tickets;

    // 统计各类型等待人数
    m_normalSavingsWaiting = 0;
    m_normalConsultingWaiting = 0;
    m_vipSavingsWaiting = 0;
    m_vipConsultingWaiting = 0;

    for (int i = 0; i < tickets.size(); ++i) {
        const Ticket& ticket = tickets.at(i);
        if (ticket.getUserType() == Ticket::NORMAL) {
            if (ticket.getBusinessType() == Ticket::SAVINGS) {
                m_normalSavingsWaiting++;
            } else {
                m_normalConsultingWaiting++;
            }
        } else {
            if (ticket.getBusinessType() == Ticket::SAVINGS) {
                m_vipSavingsWaiting++;
            } else {
                m_vipConsultingWaiting++;
            }
        }
    }

    updateWaitingCounts();
    updateQueueDisplay();
}

void CallingMachineWindow::onTicketAddedBroadcast(const Ticket& ticket)
{
    // 更新等待计数
    if (ticket.getUserType() == Ticket::NORMAL) {
        if (ticket.getBusinessType() == Ticket::SAVINGS) {
            m_normalSavingsWaiting++;
        } else {
            m_normalConsultingWaiting++;
        }
    } else {
        if (ticket.getBusinessType() == Ticket::SAVINGS) {
            m_vipSavingsWaiting++;
        } else {
            m_vipConsultingWaiting++;
        }
    }

    m_totalWaiting++;
    m_waitingTickets.append(ticket);

    updateWaitingCounts();
    updateQueueDisplay();

    addLogEntry(QString::fromUtf8("新增票据: %1").arg(ticket.getTicketNumber()));
}

void CallingMachineWindow::onTicketCalledBroadcast(const Ticket& ticket, int windowId)
{
    if (windowId == m_windowId) {
        // 本窗口的叫号
        m_currentTicket = ticket;
        m_hasCurrentTicket = true;
        m_callCount = ticket.getCallCount();
        m_lastCallTime = QDateTime::currentDateTime();

        updateCurrentTicket();
        updateButtonStates();
        updateTicketDisplay();

        // 启动超时定时器
        m_callTimer->start();
    }

    // 从等待队列中移除
    for (int i = 0; i < m_waitingTickets.size(); ++i) {
        if (m_waitingTickets.at(i) == ticket) {
            m_waitingTickets.removeAt(i);
            break;
        }
    }

    // 减少等待计数
    if (ticket.getUserType() == Ticket::NORMAL) {
        if (ticket.getBusinessType() == Ticket::SAVINGS) {
            m_normalSavingsWaiting = qMax(0, m_normalSavingsWaiting - 1);
        } else {
            m_normalConsultingWaiting = qMax(0, m_normalConsultingWaiting - 1);
        }
    } else {
        if (ticket.getBusinessType() == Ticket::SAVINGS) {
            m_vipSavingsWaiting = qMax(0, m_vipSavingsWaiting - 1);
        } else {
            m_vipConsultingWaiting = qMax(0, m_vipConsultingWaiting - 1);
        }
    }

    m_totalWaiting = qMax(0, m_totalWaiting - 1);

    updateWaitingCounts();
    updateQueueDisplay();

    addLogEntry(QString::fromUtf8("%1号窗口叫号: %2").arg(windowId).arg(ticket.getTicketNumber()));
}

void CallingMachineWindow::onTicketFinishedBroadcast(const Ticket& ticket, int windowId)
{
    if (windowId == m_windowId) {
        // 本窗口完成的票据
        m_currentTicket = Ticket();
        m_hasCurrentTicket = false;
        m_callCount = 0;
        m_callTimer->stop();

        updateCurrentTicket();
        updateButtonStates();
        updateTicketDisplay();
    }

    addLogEntry(QString::fromUtf8("%1号窗口完成: %2").arg(windowId).arg(ticket.getTicketNumber()));
}

void CallingMachineWindow::onTicketExpiredBroadcast(const Ticket& ticket, int windowId)
{
    if (windowId == m_windowId) {
        // 本窗口过期的票据
        m_currentTicket = Ticket();
        m_hasCurrentTicket = false;
        m_callCount = 0;
        m_callTimer->stop();

        updateCurrentTicket();
        updateButtonStates();
        updateTicketDisplay();
    }

    addLogEntry(QString::fromUtf8("%1号窗口票据过期: %2").arg(windowId).arg(ticket.getTicketNumber()));
}

void CallingMachineWindow::onQueueChangedBroadcast()
{
    // 混合模式：只更新界面显示，不重新加载文件（避免无限循环）
    m_totalWaiting = m_queueManager->getWaitingCount();
    m_normalSavingsWaiting = m_queueManager->getWaitingCount(Ticket::NORMAL, Ticket::SAVINGS);
    m_normalConsultingWaiting = m_queueManager->getWaitingCount(Ticket::NORMAL, Ticket::CONSULTING);
    m_vipSavingsWaiting = m_queueManager->getWaitingCount(Ticket::VIP, Ticket::SAVINGS);
    m_vipConsultingWaiting = m_queueManager->getWaitingCount(Ticket::VIP, Ticket::CONSULTING);

    // 同步等待队列到缓存
    m_waitingTickets = m_queueManager->getWaitingTickets();

    updateWaitingCounts();
    updateQueueDisplay();
}

void CallingMachineWindow::onLocalTicketCalled(const Ticket& ticket)
{
    // 简化处理：直接更新界面，不调用复杂的广播函数
    qDebug() << "onLocalTicketCalled called for ticket:" << ticket.getTicketNumber();

    // 更新界面显示
    updateWaitingCounts();
    updateQueueDisplay();

    addLogEntry(QString("Ticket called: %1").arg(ticket.getTicketNumber()));
    qDebug() << "onLocalTicketCalled completed";
}

void CallingMachineWindow::onLocalTicketFinished(const Ticket& ticket)
{
    // 简化处理：直接更新界面
    qDebug() << "onLocalTicketFinished called for ticket:" << ticket.getTicketNumber();
    updateWaitingCounts();
    updateQueueDisplay();
    addLogEntry(QString("Ticket finished: %1").arg(ticket.getTicketNumber()));
}

void CallingMachineWindow::onLocalTicketExpired(const Ticket& ticket)
{
    // 简化处理：直接更新界面
    qDebug() << "onLocalTicketExpired called for ticket:" << ticket.getTicketNumber();
    updateWaitingCounts();
    updateQueueDisplay();
    addLogEntry(QString("Ticket expired: %1").arg(ticket.getTicketNumber()));
}

void CallingMachineWindow::onUpdateDisplay()
{
    updateTimeDisplay();
}

void CallingMachineWindow::onRequestQueueStatus()
{
    // 混合模式：无论是否连接网络，都要同步本地文件
    m_queueManager->loadFromFile("data/queue_data.txt");

    // 更新等待人数显示
    m_totalWaiting = m_queueManager->getWaitingCount();
    m_normalSavingsWaiting = m_queueManager->getWaitingCount(Ticket::NORMAL, Ticket::SAVINGS);
    m_normalConsultingWaiting = m_queueManager->getWaitingCount(Ticket::NORMAL, Ticket::CONSULTING);
    m_vipSavingsWaiting = m_queueManager->getWaitingCount(Ticket::VIP, Ticket::SAVINGS);
    m_vipConsultingWaiting = m_queueManager->getWaitingCount(Ticket::VIP, Ticket::CONSULTING);

    // 同步等待队列到缓存（用于网络模式显示）
    m_waitingTickets = m_queueManager->getWaitingTickets();

    updateWaitingCounts();
    updateQueueDisplay();

    // 混合模式：不发送网络请求，避免与测试服务器的兼容性问题
}

void CallingMachineWindow::onCallTimeout()
{
    if (m_hasCurrentTicket) {
        m_callCount++;

        if (m_callCount >= 3) {
            // 超过最大叫号次数，票据过期
            QString ticketNumber = m_currentTicket.getTicketNumber();
            addLogEntry(QString("Ticket %1 expired after %2 calls - Customer did not respond").arg(ticketNumber).arg(m_callCount));

            // 从队列中移除过期票据
            m_queueManager->expireCurrent();

            // 清除当前票据
            m_currentTicket = Ticket();
            m_hasCurrentTicket = false;
            m_callCount = 0;
            m_callTimer->stop();

            updateCurrentTicket();
            updateButtonStates();
            updateTicketDisplay();

            // 保存数据
            if (!m_queueManager->saveToFile("data/queue_data.txt")) {
                addLogEntry("Warning: Failed to save queue data to file");
            }

            // 更新等待人数和队列显示
            updateWaitingCounts();
            updateQueueDisplay();

            // 显示友好的提示信息
            QString businessType = getBusinessTypeFromTicket(ticketNumber);
            QString message = QString("Ticket %1 (%2) has expired due to no response after 3 calls.\n\n"
                                    "If you are the customer for this ticket, please take a new number to continue your service.\n\n"
                                    "Thank you for your understanding.")
                                    .arg(ticketNumber)
                                    .arg(businessType);

            showMessage("Ticket Expired", message);

            addLogEntry("Ticket expired - Customer should take a new number");
        } else {
            // 继续等待
            addLogEntry(QString("Waiting for customer response (attempt %1/3)").arg(m_callCount));
        }
    }
}

void CallingMachineWindow::onConnectionTimeout()
{
    if (!m_networkConnected) {
        // 连接超时，切换到离线模式
        addLogEntry("Connection timeout - Running in offline mode");
        m_networkConnected = false;
        updateConnectionStatus();
        updateButtonStates();
    }
}

void CallingMachineWindow::updateTimeDisplay()
{
    m_timeLabel->setText(formatTime());
}

void CallingMachineWindow::updateConnectionStatus()
{
    if (m_networkConnected) {
        m_connectionStatusLabel->setText("Connection Status: Connected");
        m_connectionStatusLabel->setStyleSheet("QLabel { color: #27ae60; }");
    } else {
        m_connectionStatusLabel->setText("Connection Status: Disconnected");
        m_connectionStatusLabel->setStyleSheet("QLabel { color: #e74c3c; }");
    }
}

void CallingMachineWindow::updateWaitingCounts()
{
    m_totalWaitingLabel->setText(QString("Total Waiting: %1").arg(m_totalWaiting));
    m_normalSavingsWaitingLabel->setText(QString("Normal Savings (CA) Waiting: %1").arg(m_normalSavingsWaiting));
    m_normalConsultingWaitingLabel->setText(QString("Normal Consulting (CB) Waiting: %1").arg(m_normalConsultingWaiting));
    m_vipSavingsWaitingLabel->setText(QString("VIP Savings (VA) Waiting: %1").arg(m_vipSavingsWaiting));
    m_vipConsultingWaitingLabel->setText(QString("VIP Consulting (VB) Waiting: %1").arg(m_vipConsultingWaiting));
}

void CallingMachineWindow::updateCurrentTicket()
{
    if (!m_hasCurrentTicket) {
        m_currentTicketLabel->setText("No Current Ticket");
        m_currentStatusLabel->setText("Window Available");
        m_currentStatusLabel->setStyleSheet("QLabel { color: #27ae60; }");
        m_callCountLabel->setText("");
    } else {
        m_currentTicketLabel->setText(QString("Current: %1").arg(m_currentTicket.getDisplayText()));
        m_currentStatusLabel->setText("In Service");
        m_currentStatusLabel->setStyleSheet("QLabel { color: #e74c3c; }");
        m_callCountLabel->setText(QString("Call Count: %1/3").arg(m_callCount));
    }
}

void CallingMachineWindow::updateButtonStates()
{
    // 叫下一号按钮：队列不为空且当前无票据时可用
    m_callNextBtn->setEnabled(!m_hasCurrentTicket && m_totalWaiting > 0);

    // 重复呼叫按钮：有当前票据且未达最大呼叫次数时可用
    m_callCurrentBtn->setEnabled(m_hasCurrentTicket && m_callCount < 3);

    // 完成办理按钮：有当前票据时可用
    m_finishCurrentBtn->setEnabled(m_hasCurrentTicket);

    // 重置按钮：始终可用
    m_resetBtn->setEnabled(true);
}

void CallingMachineWindow::updateQueueDisplay()
{
    QString text;

    // 混合模式：统一使用本地队列管理器的数据
    QList<Ticket> waitingTickets = m_queueManager->getWaitingTickets();
    text += QString("Waiting Queue (Total: %1):\n").arg(waitingTickets.size());

    for (int i = 0; i < qMin(10, waitingTickets.size()); ++i) {
        const Ticket& ticket = waitingTickets.at(i);
        text += QString("%1. %2 - %3\n")
                .arg(i + 1, 2)
                .arg(ticket.getTicketNumber())
                .arg(ticket.getDisplayText());
    }

    if (waitingTickets.size() > 10) {
        text += QString("... %1 more waiting\n").arg(waitingTickets.size() - 10);
    }

    m_queueText->setPlainText(text);
}

void CallingMachineWindow::updateTicketDisplay()
{
    if (!m_hasCurrentTicket) {
        m_ticketDisplay->display("-----");
    } else {
        m_ticketDisplay->display(m_currentTicket.getTicketNumber());
    }
}

void CallingMachineWindow::addLogEntry(const QString& message)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString logEntry = QString("[%1] %2\n").arg(timestamp).arg(message);

    m_logText->append(logEntry.trimmed());

    // 滚动到底部
    QScrollBar* scrollBar = m_logText->verticalScrollBar();
    scrollBar->setValue(scrollBar->maximum());

    // 限制日志行数
    QStringList lines = m_logText->toPlainText().split('\n');
    if (lines.size() > 100) {
        lines = lines.mid(lines.size() - 100);
        m_logText->setPlainText(lines.join("\n"));
        scrollBar->setValue(scrollBar->maximum());
    }
}

QString CallingMachineWindow::formatTime() const
{
    QDateTime now = QDateTime::currentDateTime();
    return now.toString("yyyy-MM-dd hh:mm:ss dddd");
}

QString CallingMachineWindow::getBusinessTypeFromTicket(const QString& ticketNumber) const
{
    if (ticketNumber.length() >= 2) {
        QString prefix = ticketNumber.left(2);
        if (prefix == "CA") {
            return "Normal Savings";
        } else if (prefix == "CB") {
            return "Normal Consulting";
        } else if (prefix == "VA") {
            return "VIP Savings";
        } else if (prefix == "VB") {
            return "VIP Consulting";
        }
    }
    return "Unknown Service";
}

QString CallingMachineWindow::getBusinessTypeText(Ticket::BusinessType businessType) const
{
    switch (businessType) {
    case Ticket::SAVINGS:
        return "Savings";
    case Ticket::CONSULTING:
        return "Consulting";
    default:
        return "Unknown";
    }
}

QString CallingMachineWindow::getUserTypeText(Ticket::UserType userType) const
{
    switch (userType) {
    case Ticket::NORMAL:
        return "Normal";
    case Ticket::VIP:
        return "VIP";
    default:
        return "Unknown";
    }
}

void CallingMachineWindow::showMessage(const QString& title, const QString& message)
{
    QMessageBox::information(this, title, message);
}

void CallingMachineWindow::playCallSound()
{
    // 这里可以添加播放叫号声音的代码
    // 由于是开发板环境，暂不实现
    // 可以使用QSound或者调用系统音频接口
}
