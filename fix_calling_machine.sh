#!/bin/bash

echo "=== 修复叫号机编译问题 ==="

# 清理之前的编译文件
echo "清理之前的编译文件..."
rm -rf build lib bin
mkdir -p build lib bin bin/data

# 编译 common 库
echo "编译 common 库..."
cd common
qmake-qt4 common.pro
make clean
make
cd ..

# 检查 common 库是否编译成功
if [ ! -f "lib/libcommon.a" ]; then
    echo "错误: common 库编译失败"
    exit 1
fi
echo "common 库编译成功"

# 编译叫号机
echo "编译叫号机..."
cd calling_machine
qmake-qt4 calling_machine.pro
make clean
make
cd ..

# 检查叫号机是否编译成功
if [ ! -f "bin/calling_machine" ]; then
    echo "错误: 叫号机编译失败"
    exit 1
fi
echo "叫号机编译成功"

# 编译取号机
echo "编译取号机..."
cd ticket_machine
qmake-qt4 ticket_machine.pro
make clean
make
cd ..

# 检查取号机是否编译成功
if [ ! -f "bin/ticket_machine" ]; then
    echo "错误: 取号机编译失败"
    exit 1
fi
echo "取号机编译成功"

echo "=== 编译完成 ==="
echo "可执行文件位置:"
echo "  叫号机: bin/calling_machine"
echo "  取号机: bin/ticket_machine"

echo ""
echo "运行测试:"
echo "  ./bin/calling_machine"
echo "  ./bin/ticket_machine"
