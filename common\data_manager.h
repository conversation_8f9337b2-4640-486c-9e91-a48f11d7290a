#ifndef DATA_MANAGER_H
#define DATA_MANAGER_H

#include "queue_manager.h"
#include <QObject>
#include <QString>
#include <QTimer>
#include <QDate>

/**
 * 数据管理器
 * 负责数据的持久化存储和定时清理
 */
class DataManager : public QObject
{
    Q_OBJECT

public:
    explicit DataManager(QueueManager* queueManager, QObject *parent = 0);
    ~DataManager();
    
    // 数据文件路径设置
    void setDataFilePath(const QString& filePath);
    QString getDataFilePath() const;
    
    // 备份文件路径设置
    void setBackupFilePath(const QString& filePath);
    QString getBackupFilePath() const;
    
    // 自动保存设置
    void setAutoSaveEnabled(bool enabled);
    bool isAutoSaveEnabled() const;
    void setAutoSaveInterval(int intervalMs);
    int getAutoSaveInterval() const;
    
    // 数据操作
    bool saveData();
    bool loadData();
    bool backupData();
    bool restoreFromBackup();
    
    // 日期管理
    bool isNewDay() const;
    void checkAndResetDaily();
    
    // 数据清理
    void clearAllData();
    void clearExpiredData();
    
    // 启动和停止
    void start();
    void stop();

signals:
    void dataSaved();
    void dataLoaded();
    void dataBackedUp();
    void dataRestored();
    void dailyReset();
    void saveError(const QString& error);
    void loadError(const QString& error);

private slots:
    void onAutoSave();
    void onDailyCheck();
    void onQueueChanged();

private:
    QueueManager* m_queueManager;
    QString m_dataFilePath;
    QString m_backupFilePath;
    
    // 自动保存
    bool m_autoSaveEnabled;
    int m_autoSaveInterval;
    QTimer* m_autoSaveTimer;
    
    // 日期检查
    QTimer* m_dailyCheckTimer;
    QDate m_lastSaveDate;
    
    // 数据变化标记
    bool m_dataChanged;
    
    void initTimers();
    void updateLastSaveDate();
    QString generateBackupFileName() const;
    bool copyFile(const QString& source, const QString& destination);
};

#endif // DATA_MANAGER_H
