#include "message_protocol.h"
#include <QDebug>

MessageProtocol::MessageProtocol()
{
}

QString MessageProtocol::buildMessage(MessageType type, const QStringList& params)
{
    QStringList parts;
    parts.append(QString::number(static_cast<int>(type)));
    parts.append(joinParams(params));
    return parts.join(QString("|"));
}

QStringList MessageProtocol::splitMessage(const QString& message)
{
    return message.split(QString("|"));
}

QString MessageProtocol::joinParams(const QStringList& params)
{
    return params.join(QString(";"));
}

QString MessageProtocol::buildTakeNumberRequest(Ticket::UserType userType, Ticket::BusinessType businessType)
{
    QStringList params;
    params.append(QString::number(static_cast<int>(userType)));
    params.append(QString::number(static_cast<int>(businessType)));
    return buildMessage(TAKE_NUMBER_REQUEST, params);
}

QString MessageProtocol::buildTakeNumberResponse(const Ticket& ticket, bool success, const QString& error)
{
    QStringList params;
    params.append(success ? "1" : "0");
    if (success) {
        params.append(encodeTicket(ticket));
    } else {
        params.append(error);
    }
    return buildMessage(TAKE_NUMBER_RESPONSE, params);
}

QString MessageProtocol::buildCallNextRequest(int windowId)
{
    QStringList params;
    params.append(QString::number(windowId));
    return buildMessage(CALL_NEXT_REQUEST, params);
}

QString MessageProtocol::buildCallNextResponse(const Ticket& ticket, bool success, const QString& error)
{
    QStringList params;
    params.append(success ? "1" : "0");
    if (success) {
        params.append(encodeTicket(ticket));
    } else {
        params.append(error);
    }
    return buildMessage(CALL_NEXT_RESPONSE, params);
}

QString MessageProtocol::buildCallCurrentRequest(int windowId)
{
    QStringList params;
    params.append(QString::number(windowId));
    return buildMessage(CALL_CURRENT_REQUEST, params);
}

QString MessageProtocol::buildCallCurrentResponse(bool success, const QString& error)
{
    QStringList params;
    params.append(success ? "1" : "0");
    if (!success) {
        params.append(error);
    }
    return buildMessage(CALL_CURRENT_RESPONSE, params);
}

QString MessageProtocol::buildFinishCurrentRequest(int windowId)
{
    QStringList params;
    params.append(QString::number(windowId));
    return buildMessage(FINISH_CURRENT_REQUEST, params);
}

QString MessageProtocol::buildFinishCurrentResponse(bool success, const QString& error)
{
    QStringList params;
    params.append(success ? "1" : "0");
    if (!success) {
        params.append(error);
    }
    return buildMessage(FINISH_CURRENT_RESPONSE, params);
}

QString MessageProtocol::buildQueueStatusRequest()
{
    QStringList params;
    return buildMessage(QUEUE_STATUS_REQUEST, params);
}

QString MessageProtocol::buildQueueStatusResponse(int waitingCount, const QList<Ticket>& tickets)
{
    QStringList params;
    params.append(QString::number(waitingCount));
    params.append(encodeTicketList(tickets));
    return buildMessage(QUEUE_STATUS_RESPONSE, params);
}

QString MessageProtocol::buildTicketAddedBroadcast(const Ticket& ticket)
{
    QStringList params;
    params.append(encodeTicket(ticket));
    return buildMessage(TICKET_ADDED_BROADCAST, params);
}

QString MessageProtocol::buildTicketCalledBroadcast(const Ticket& ticket, int windowId)
{
    QStringList params;
    params.append(encodeTicket(ticket));
    params.append(QString::number(windowId));
    return buildMessage(TICKET_CALLED_BROADCAST, params);
}

QString MessageProtocol::buildTicketFinishedBroadcast(const Ticket& ticket, int windowId)
{
    QStringList params;
    params.append(encodeTicket(ticket));
    params.append(QString::number(windowId));
    return buildMessage(TICKET_FINISHED_BROADCAST, params);
}

QString MessageProtocol::buildTicketExpiredBroadcast(const Ticket& ticket, int windowId)
{
    QStringList params;
    params.append(encodeTicket(ticket));
    params.append(QString::number(windowId));
    return buildMessage(TICKET_EXPIRED_BROADCAST, params);
}

QString MessageProtocol::buildQueueChangedBroadcast()
{
    QStringList params;
    return buildMessage(QUEUE_CHANGED_BROADCAST, params);
}

QString MessageProtocol::buildHeartbeat(ClientType clientType, int clientId)
{
    QStringList params;
    params.append(QString::number(static_cast<int>(clientType)));
    params.append(QString::number(clientId));
    return buildMessage(HEARTBEAT, params);
}

QString MessageProtocol::buildErrorResponse(const QString& error)
{
    QStringList params;
    params.append(error);
    return buildMessage(ERROR_RESPONSE, params);
}

QString MessageProtocol::buildClientRegister(ClientType clientType, int clientId, const QString& clientName)
{
    QStringList params;
    params.append(QString::number(static_cast<int>(clientType)));
    params.append(QString::number(clientId));
    params.append(clientName);
    return buildMessage(CLIENT_REGISTER, params);
}

QString MessageProtocol::buildClientUnregister(ClientType clientType, int clientId)
{
    QStringList params;
    params.append(QString::number(static_cast<int>(clientType)));
    params.append(QString::number(clientId));
    return buildMessage(CLIENT_UNREGISTER, params);
}

bool MessageProtocol::parseMessage(const QString& message, MessageType& type, QStringList& params)
{
    QStringList parts = splitMessage(message);
    if (parts.size() < 1) {
        return false;
    }

    bool ok;
    int typeInt = parts.at(0).toInt(&ok);
    if (!ok) {
        return false;
    }

    type = static_cast<MessageType>(typeInt);

    if (parts.size() > 1) {
        params = parts.at(1).split(QString(";"));
    } else {
        params.clear();
    }

    return true;
}

MessageProtocol::MessageType MessageProtocol::getMessageType(const QString& message)
{
    QStringList parts = splitMessage(message);
    if (parts.size() < 1) {
        return ERROR_RESPONSE;
    }

    bool ok;
    int typeInt = parts.at(0).toInt(&ok);
    if (!ok) {
        return ERROR_RESPONSE;
    }

    return static_cast<MessageType>(typeInt);
}

QString MessageProtocol::encodeTicket(const Ticket& ticket)
{
    return ticket.toString();
}

bool MessageProtocol::decodeTicket(const QString& ticketStr, Ticket& ticket)
{
    return ticket.fromString(ticketStr);
}

QString MessageProtocol::encodeTicketList(const QList<Ticket>& tickets)
{
    QStringList ticketStrings;
    for (int i = 0; i < tickets.size(); ++i) {
        ticketStrings.append(encodeTicket(tickets.at(i)));
    }
    return ticketStrings.join(QString(","));
}

bool MessageProtocol::decodeTicketList(const QString& ticketsStr, QList<Ticket>& tickets)
{
    tickets.clear();
    if (ticketsStr.isEmpty()) {
        return true;
    }
    
    QStringList ticketStrings = ticketsStr.split(QString(","));
    for (int i = 0; i < ticketStrings.size(); ++i) {
        Ticket ticket;
        if (decodeTicket(ticketStrings.at(i), ticket)) {
            tickets.append(ticket);
        } else {
            return false;
        }
    }
    
    return true;
}

bool MessageProtocol::parseTakeNumberRequest(const QStringList& params, Ticket::UserType& userType, Ticket::BusinessType& businessType)
{
    if (params.size() < 2) {
        return false;
    }

    bool ok1, ok2;
    int userTypeInt = params.at(0).toInt(&ok1);
    int businessTypeInt = params.at(1).toInt(&ok2);

    if (!ok1 || !ok2) {
        return false;
    }

    userType = static_cast<Ticket::UserType>(userTypeInt);
    businessType = static_cast<Ticket::BusinessType>(businessTypeInt);

    return true;
}

bool MessageProtocol::parseTakeNumberResponse(const QStringList& params, Ticket& ticket, bool& success, QString& error)
{
    if (params.size() < 2) {
        return false;
    }

    success = (params.at(0) == "1");

    if (success) {
        return decodeTicket(params.at(1), ticket);
    } else {
        error = params.at(1);
        return true;
    }
}

bool MessageProtocol::parseCallNextRequest(const QStringList& params, int& windowId)
{
    if (params.size() < 1) {
        return false;
    }

    bool ok;
    windowId = params.at(0).toInt(&ok);
    return ok;
}

bool MessageProtocol::parseCallNextResponse(const QStringList& params, Ticket& ticket, bool& success, QString& error)
{
    if (params.size() < 2) {
        return false;
    }

    success = (params.at(0) == "1");

    if (success) {
        return decodeTicket(params.at(1), ticket);
    } else {
        error = params.at(1);
        return true;
    }
}

bool MessageProtocol::parseCallCurrentRequest(const QStringList& params, int& windowId)
{
    if (params.size() < 1) {
        return false;
    }

    bool ok;
    windowId = params.at(0).toInt(&ok);
    return ok;
}

bool MessageProtocol::parseCallCurrentResponse(const QStringList& params, bool& success, QString& error)
{
    if (params.size() < 1) {
        return false;
    }

    success = (params.at(0) == "1");

    if (!success && params.size() > 1) {
        error = params.at(1);
    }

    return true;
}

bool MessageProtocol::parseFinishCurrentRequest(const QStringList& params, int& windowId)
{
    if (params.size() < 1) {
        return false;
    }

    bool ok;
    windowId = params.at(0).toInt(&ok);
    return ok;
}

bool MessageProtocol::parseFinishCurrentResponse(const QStringList& params, bool& success, QString& error)
{
    if (params.size() < 1) {
        return false;
    }

    success = (params.at(0) == "1");

    if (!success && params.size() > 1) {
        error = params.at(1);
    }

    return true;
}

bool MessageProtocol::parseQueueStatusResponse(const QStringList& params, int& waitingCount, QList<Ticket>& tickets)
{
    if (params.size() < 2) {
        return false;
    }

    bool ok;
    waitingCount = params.at(0).toInt(&ok);
    if (!ok) {
        return false;
    }

    return decodeTicketList(params.at(1), tickets);
}

bool MessageProtocol::parseTicketBroadcast(const QStringList& params, Ticket& ticket, int& windowId)
{
    if (params.size() < 2) {
        return false;
    }

    if (!decodeTicket(params.at(0), ticket)) {
        return false;
    }

    bool ok;
    windowId = params.at(1).toInt(&ok);
    return ok;
}

bool MessageProtocol::parseHeartbeat(const QStringList& params, ClientType& clientType, int& clientId)
{
    if (params.size() < 2) {
        return false;
    }

    bool ok1, ok2;
    int clientTypeInt = params.at(0).toInt(&ok1);
    clientId = params.at(1).toInt(&ok2);

    if (!ok1 || !ok2) {
        return false;
    }

    clientType = static_cast<ClientType>(clientTypeInt);
    return true;
}

bool MessageProtocol::parseClientRegister(const QStringList& params, ClientType& clientType, int& clientId, QString& clientName)
{
    if (params.size() < 3) {
        return false;
    }

    bool ok1, ok2;
    int clientTypeInt = params.at(0).toInt(&ok1);
    clientId = params.at(1).toInt(&ok2);

    if (!ok1 || !ok2) {
        return false;
    }

    clientType = static_cast<ClientType>(clientTypeInt);
    clientName = params.at(2);
    return true;
}

bool MessageProtocol::parseClientUnregister(const QStringList& params, ClientType& clientType, int& clientId)
{
    if (params.size() < 2) {
        return false;
    }

    bool ok1, ok2;
    int clientTypeInt = params.at(0).toInt(&ok1);
    clientId = params.at(1).toInt(&ok2);

    if (!ok1 || !ok2) {
        return false;
    }

    clientType = static_cast<ClientType>(clientTypeInt);
    return true;
}
