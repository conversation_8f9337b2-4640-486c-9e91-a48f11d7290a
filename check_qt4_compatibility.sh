#!/bin/bash

# Qt 4.8.5 兼容性检查脚本
echo "=== Qt 4.8.5 兼容性检查 ==="
echo ""

# 检查Qt版本
echo "1. Qt版本检查:"
if command -v qmake &> /dev/null; then
    QT_VERSION=$(qmake -version | grep "Using Qt version" | cut -d' ' -f4)
    echo "   当前Qt版本: $QT_VERSION"
    
    if [[ $QT_VERSION =~ ^4\. ]]; then
        echo "   ✅ Qt 4.x 版本检测通过"
    else
        echo "   ⚠️  警告: 检测到非Qt 4.x版本，可能存在兼容性问题"
    fi
else
    echo "   ❌ 未找到qmake命令"
    exit 1
fi
echo ""

# 检查必要的Qt模块
echo "2. Qt模块检查:"
MODULES=("QtCore" "QtGui" "QtNetwork")
for module in "${MODULES[@]}"; do
    if pkg-config --exists $module 2>/dev/null; then
        echo "   ✅ $module 可用"
    else
        echo "   ❌ $module 不可用"
    fi
done
echo ""

# 检查代码中的Qt 4.8.5兼容性问题
echo "3. 代码兼容性检查:"

# 检查是否还有 << 操作符用于QStringList
echo "   检查QStringList << 操作符..."
if grep -r "<<.*QString\|QString.*<<" common/ ticket_machine/ calling_machine/ --include="*.cpp" --include="*.h" | grep -v "QTextStream\|QDebug\|operator<<" | head -5; then
    echo "   ⚠️  发现可能的QStringList << 操作符使用"
else
    echo "   ✅ 未发现QStringList << 操作符问题"
fi

# 检查是否还有数组访问操作符
echo "   检查数组访问操作符..."
if grep -r "\[\s*[0-9]\+\s*\]" common/ ticket_machine/ calling_machine/ --include="*.cpp" | grep -v "array\|buffer" | head -5; then
    echo "   ⚠️  发现可能的数组访问操作符使用"
else
    echo "   ✅ 未发现数组访问操作符问题"
fi

# 检查字符串split/join调用
echo "   检查字符串split/join调用..."
if grep -r "\.split\s*(\s*[\"'][^\"']*[\"']\s*)\|\.join\s*(\s*[\"'][^\"']*[\"']\s*)" common/ ticket_machine/ calling_machine/ --include="*.cpp" | head -5; then
    echo "   ✅ 发现正确的split/join调用格式"
else
    echo "   ⚠️  可能存在split/join格式问题"
fi

echo ""

# 检查C++11特性使用
echo "4. C++11特性检查:"
CPP11_FEATURES=("auto " "lambda" "nullptr" "override" "final" "constexpr")
for feature in "${CPP11_FEATURES[@]}"; do
    if grep -r "$feature" common/ ticket_machine/ calling_machine/ --include="*.cpp" --include="*.h" >/dev/null 2>&1; then
        echo "   ⚠️  发现C++11特性: $feature"
    fi
done
echo "   ✅ C++11特性检查完成"
echo ""

# 检查项目文件
echo "5. 项目文件检查:"
if [ -f "bank_queue_system.pro" ]; then
    echo "   ✅ 主项目文件存在"
    
    # 检查Qt模块配置
    if grep -q "QT.*network" bank_queue_system.pro; then
        echo "   ✅ 网络模块已配置"
    else
        echo "   ⚠️  网络模块配置可能缺失"
    fi
else
    echo "   ❌ 主项目文件不存在"
fi

if [ -f "common/common.pro" ]; then
    echo "   ✅ 公共库项目文件存在"
else
    echo "   ❌ 公共库项目文件不存在"
fi
echo ""

# 检查编译脚本
echo "6. 编译脚本检查:"
if [ -f "build_qt4.sh" ]; then
    echo "   ✅ Qt 4.8.5专用编译脚本存在"
    if [ -x "build_qt4.sh" ]; then
        echo "   ✅ 编译脚本可执行"
    else
        echo "   ⚠️  编译脚本不可执行，运行: chmod +x build_qt4.sh"
    fi
else
    echo "   ❌ Qt 4.8.5专用编译脚本不存在"
fi
echo ""

echo "=== 兼容性检查完成 ==="
echo ""
echo "建议的编译步骤:"
echo "1. chmod +x build_qt4.sh"
echo "2. ./build_qt4.sh"
echo ""
echo "如果遇到问题，请查看 QT4_FIXES.md 文档"
