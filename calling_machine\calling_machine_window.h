#ifndef CALLING_MACHINE_WINDOW_H
#define CALLING_MACHINE_WINDOW_H

#include "../common/ticket.h"
#include "../common/queue_manager.h"
#include "../common/network_client.h"
#include <QMainWindow>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QTextEdit>
#include <QTimer>
#include <QGroupBox>
#include <QLCDNumber>

/**
 * 叫号机主窗口
 * 提供叫号功能和队列状态显示
 */
class CallingMachineWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit CallingMachineWindow(int windowId = 1, QWidget *parent = 0);
    ~CallingMachineWindow();

private slots:
    // 叫号按钮槽函数
    void onCallNext();
    void onCallCurrent();
    void onFinishCurrent();
    void onResetWindow();
    
    // 网络相关槽函数
    void onNetworkConnected();
    void onNetworkDisconnected();
    void onNetworkError(const QString& error);
    void onCallNextResponse(const Ticket& ticket, bool success, const QString& error);
    void onCallCurrentResponse(bool success, const QString& error);
    void onFinishCurrentResponse(bool success, const QString& error);
    void onQueueStatusResponse(int waitingCount, const QList<Ticket>& tickets);
    
    // 广播消息槽函数
    void onTicketAddedBroadcast(const Ticket& ticket);
    void onTicketCalledBroadcast(const Ticket& ticket, int windowId);
    void onTicketFinishedBroadcast(const Ticket& ticket, int windowId);
    void onTicketExpiredBroadcast(const Ticket& ticket, int windowId);
    void onQueueChangedBroadcast();

    // 本地队列管理器槽函数
    void onLocalTicketCalled(const Ticket& ticket);
    void onLocalTicketFinished(const Ticket& ticket);
    void onLocalTicketExpired(const Ticket& ticket);
    
    // 定时器槽函数
    void onUpdateDisplay();
    void onRequestQueueStatus();
    void onCallTimeout();
    void onConnectionTimeout();

private:
    // UI组件
    QWidget* m_centralWidget;
    QVBoxLayout* m_mainLayout;
    
    // 标题区域
    QLabel* m_titleLabel;
    QLabel* m_windowLabel;
    QLabel* m_timeLabel;
    
    // 当前叫号显示区域
    QGroupBox* m_currentGroup;
    QVBoxLayout* m_currentLayout;
    QLabel* m_currentTicketLabel;
    QLabel* m_currentStatusLabel;
    QLabel* m_callCountLabel;
    QLCDNumber* m_ticketDisplay;
    
    // 叫号控制按钮区域
    QGroupBox* m_controlGroup;
    QGridLayout* m_controlLayout;
    QPushButton* m_callNextBtn;
    QPushButton* m_callCurrentBtn;
    QPushButton* m_finishCurrentBtn;
    QPushButton* m_resetBtn;
    
    // 状态显示区域
    QGroupBox* m_statusGroup;
    QVBoxLayout* m_statusLayout;
    QLabel* m_connectionStatusLabel;
    QLabel* m_totalWaitingLabel;
    QLabel* m_normalSavingsWaitingLabel;
    QLabel* m_normalConsultingWaitingLabel;
    QLabel* m_vipSavingsWaitingLabel;
    QLabel* m_vipConsultingWaitingLabel;
    
    // 队列显示区域
    QGroupBox* m_queueGroup;
    QTextEdit* m_queueText;
    
    // 操作记录区域
    QGroupBox* m_logGroup;
    QTextEdit* m_logText;
    
    // 网络和业务逻辑
    NetworkClient* m_networkClient;
    QueueManager* m_queueManager;
    
    // 定时器
    QTimer* m_displayTimer;      // 界面更新定时器
    QTimer* m_statusTimer;       // 状态查询定时器
    QTimer* m_callTimer;         // 叫号超时定时器
    
    // 状态变量
    int m_windowId;              // 窗口编号
    bool m_networkConnected;     // 网络连接状态
    Ticket m_currentTicket;      // 当前处理的票据
    bool m_hasCurrentTicket;     // 是否有当前票据
    int m_callCount;             // 当前票据呼叫次数
    QDateTime m_lastCallTime;    // 最后呼叫时间
    
    // 队列状态
    int m_totalWaiting;
    int m_normalSavingsWaiting;
    int m_normalConsultingWaiting;
    int m_vipSavingsWaiting;
    int m_vipConsultingWaiting;
    QList<Ticket> m_waitingTickets;
    
    // 初始化函数
    void initUI();
    void initNetwork();
    void initTimers();
    void setupConnections();
    
    // UI更新函数
    void updateTimeDisplay();
    void updateConnectionStatus();
    void updateWaitingCounts();
    void updateCurrentTicket();
    void updateButtonStates();
    void updateQueueDisplay();
    void updateTicketDisplay();
    
    // 业务逻辑函数
    void callNextTicket();
    void callCurrentTicket();
    void finishCurrentTicket();
    void resetWindow();
    void addLogEntry(const QString& message);

private slots:
    // 按钮槽函数
    void onCallNext();
    void onCallCurrent();
    void onFinishCurrent();
    void onResetWindow();
    void onUpdateDisplay();
    void onRequestQueueStatus();
    void onCallTimeout();
    void onConnectionTimeout();

    // 网络连接槽函数
    void connectToServer();
    void onNetworkConnected();
    void onNetworkDisconnected();
    void onNetworkError(const QString& error);
    void onCallNextResponse(const Ticket& ticket, bool success, const QString& error);
    void onCallCurrentResponse(bool success, const QString& error);
    void onFinishCurrentResponse(bool success, const QString& error);
    void onQueueStatusResponse(int waitingCount, const QList<Ticket>& tickets);

    // 广播消息槽函数
    void onTicketAddedBroadcast(const Ticket& ticket);
    void onTicketCalledBroadcast(const Ticket& ticket, int windowId);
    void onTicketFinishedBroadcast(const Ticket& ticket, int windowId);
    void onTicketExpiredBroadcast(const Ticket& ticket, int windowId);
    void onQueueChangedBroadcast();

    // 工具函数
    QString formatTime() const;
    QString getBusinessTypeText(Ticket::BusinessType businessType) const;
    QString getUserTypeText(Ticket::UserType userType) const;
    void showMessage(const QString& title, const QString& message);
    void playCallSound(); // 播放叫号声音（暂不实现）
};

#endif // CALLING_MACHINE_WINDOW_H
