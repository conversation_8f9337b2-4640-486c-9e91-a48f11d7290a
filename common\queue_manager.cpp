#include "queue_manager.h"
#include <QMutexLocker>
#include <QTextStream>
#include <QFile>
#include <QDebug>
#include <QStringList>

QueueManager::QueueManager(QObject *parent)
    : QObject(parent)
    , m_hasCurrentTicket(false)
    , m_normalSavingsCount(0)
    , m_normalConsultingCount(0)
    , m_vipSavingsCount(0)
    , m_vipConsultingCount(0)
    , m_lastDate(QDate::currentDate())
    , m_expireTimer(new QTimer(this))
{
    initCounters();
    
    // 设置过期检查定时器
    m_expireTimer->setInterval(10000); // 10秒检查一次
    connect(m_expireTimer, SIGNAL(timeout()), this, SLOT(checkExpiredTickets()));
}

QueueManager::~QueueManager()
{
    stopExpireTimer();
}

Ticket QueueManager::takeNumber(Ticket::UserType userType, Ticket::BusinessType businessType)
{
    QMutexLocker locker(&m_mutex);
    
    // 检查是否新的一天
    if (isNewDay()) {
        resetDailyNumbers();
        updateLastDate();
    }
    
    int number = getNextNumber(userType, businessType);
    Ticket ticket(userType, businessType, number);
    
    insertTicketByPriority(ticket);
    
    emit ticketAdded(ticket);
    emit queueChanged();
    
    return ticket;
}

Ticket QueueManager::callNext()
{
    qDebug() << "QueueManager::callNext() started";
    QMutexLocker locker(&m_mutex);

    if (m_waitingQueue.isEmpty()) {
        qDebug() << "Queue is empty, returning invalid ticket";
        m_hasCurrentTicket = false;
        stopExpireTimer();
        return Ticket(); // 返回无效票据
    }

    qDebug() << "Queue has" << m_waitingQueue.size() << "tickets";

    // 如果当前有票据正在处理，先完成它
    if (m_hasCurrentTicket) {
        qDebug() << "Current ticket exists, finishing it first";
        finishCurrent();
    }

    // 取出队首票据
    qDebug() << "Taking first ticket from queue";
    m_currentTicket = m_waitingQueue.takeFirst();
    m_currentTicket.setStatus(Ticket::CALLING);
    m_currentTicket.setCallTime(QDateTime::currentDateTime());
    m_currentTicket.incrementCallCount();
    m_hasCurrentTicket = true;

    qDebug() << "Starting expire timer";
    startExpireTimer();

    qDebug() << "Emitting signals for ticket:" << m_currentTicket.getTicketNumber();
    emit ticketCalled(m_currentTicket);
    emit queueChanged();

    qDebug() << "QueueManager::callNext() completed, returning ticket:" << m_currentTicket.getTicketNumber();
    return m_currentTicket;
}

bool QueueManager::callCurrent()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_hasCurrentTicket) {
        return false;
    }
    
    // 检查是否超过最大呼叫次数
    if (m_currentTicket.getCallCount() >= 3) {
        expireCurrent();
        return false;
    }
    
    m_currentTicket.setCallTime(QDateTime::currentDateTime());
    m_currentTicket.incrementCallCount();
    
    emit ticketCalled(m_currentTicket);
    
    return true;
}

void QueueManager::finishCurrent()
{
    if (!m_hasCurrentTicket) {
        return;
    }
    
    m_currentTicket.setStatus(Ticket::FINISHED);
    m_hasCurrentTicket = false;
    
    stopExpireTimer();
    
    emit ticketFinished(m_currentTicket);
    emit queueChanged();
}

void QueueManager::expireCurrent()
{
    if (!m_hasCurrentTicket) {
        return;
    }
    
    m_currentTicket.setStatus(Ticket::EXPIRED);
    m_hasCurrentTicket = false;
    
    stopExpireTimer();
    
    emit ticketExpired(m_currentTicket);
    emit queueChanged();
}

int QueueManager::getWaitingCount() const
{
    QMutexLocker locker(const_cast<QMutex*>(&m_mutex));
    return m_waitingQueue.size();
}

int QueueManager::getWaitingCount(Ticket::UserType userType, Ticket::BusinessType businessType) const
{
    QMutexLocker locker(const_cast<QMutex*>(&m_mutex));
    
    int count = 0;
    for (int i = 0; i < m_waitingQueue.size(); ++i) {
        const Ticket& ticket = m_waitingQueue.at(i);
        if (ticket.getUserType() == userType && ticket.getBusinessType() == businessType) {
            count++;
        }
    }
    return count;
}

QList<Ticket> QueueManager::getWaitingTickets() const
{
    QMutexLocker locker(const_cast<QMutex*>(&m_mutex));
    return m_waitingQueue;
}

Ticket QueueManager::getCurrentTicket() const
{
    QMutexLocker locker(const_cast<QMutex*>(&m_mutex));
    return m_currentTicket;
}

bool QueueManager::hasCurrentTicket() const
{
    QMutexLocker locker(const_cast<QMutex*>(&m_mutex));
    return m_hasCurrentTicket;
}

int QueueManager::getNextNumber(Ticket::UserType userType, Ticket::BusinessType businessType)
{
    int& counter = getCounterRef(userType, businessType);
    return ++counter;
}

void QueueManager::resetDailyNumbers()
{
    m_normalSavingsCount = 0;
    m_normalConsultingCount = 0;
    m_vipSavingsCount = 0;
    m_vipConsultingCount = 0;
    
    // 清空队列
    clearQueue();
}

bool QueueManager::isNewDay() const
{
    return QDate::currentDate() != m_lastDate;
}

void QueueManager::updateLastDate()
{
    m_lastDate = QDate::currentDate();
}

bool QueueManager::saveToFile(const QString& filename)
{
    QMutexLocker locker(&m_mutex);

    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "Failed to open file for writing:" << filename;
        return false;
    }

    QTextStream out(&file);

    // 保存日期和计数器
    out << "DATE:" << m_lastDate.toString("yyyy-MM-dd") << "\n";
    out << "COUNTERS:" << m_normalSavingsCount << "," << m_normalConsultingCount
        << "," << m_vipSavingsCount << "," << m_vipConsultingCount << "\n";

    // 保存当前票据
    if (m_hasCurrentTicket) {
        out << "CURRENT:" << m_currentTicket.toString() << "\n";
    }

    // 保存等待队列
    out << "QUEUE_SIZE:" << m_waitingQueue.size() << "\n";
    for (int i = 0; i < m_waitingQueue.size(); ++i) {
        out << "TICKET:" << m_waitingQueue.at(i).toString() << "\n";
    }

    file.close();
    return true;
}

bool QueueManager::loadFromFile(const QString& filename)
{
    qDebug() << "QueueManager::loadFromFile started for:" << filename;
    QMutexLocker locker(&m_mutex);
    qDebug() << "Mutex locked";

    QFile file(filename);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "Failed to open file for reading:" << filename;
        return false;
    }
    qDebug() << "File opened successfully";

    QTextStream in(&file);
    clearQueue();
    qDebug() << "Queue cleared, starting to read lines";

    int lineCount = 0;
    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        lineCount++;
        if (line.isEmpty()) continue;

        if (line.startsWith("DATE:")) {
            QString dateStr = line.mid(5);
            m_lastDate = QDate::fromString(dateStr, "yyyy-MM-dd");
        }
        else if (line.startsWith("COUNTERS:")) {
            QString countersStr = line.mid(9);
            QStringList counters = countersStr.split(QString(","));
            if (counters.size() >= 4) {
                m_normalSavingsCount = counters.at(0).toInt();
                m_normalConsultingCount = counters.at(1).toInt();
                m_vipSavingsCount = counters.at(2).toInt();
                m_vipConsultingCount = counters.at(3).toInt();
            }
        }
        else if (line.startsWith("CURRENT:")) {
            QString ticketStr = line.mid(8);
            Ticket ticket;
            if (ticket.fromString(ticketStr)) {
                m_currentTicket = ticket;
                m_hasCurrentTicket = true;
            }
        }
        else if (line.startsWith("TICKET:")) {
            QString ticketStr = line.mid(7);
            Ticket ticket;
            if (ticket.fromString(ticketStr)) {
                m_waitingQueue.append(ticket);
            } else {
                qDebug() << "Failed to parse ticket:" << ticketStr;
            }
        }
    }

    file.close();
    qDebug() << "File closed, checking if new day...";

    // 如果是新的一天，重置数据
    if (isNewDay()) {
        qDebug() << "New day detected, resetting...";
        resetDailyNumbers();
        updateLastDate();
    }

    // 不在 loadFromFile 中发送 queueChanged 信号，避免初始化时的无限循环
    qDebug() << "QueueManager::loadFromFile completed successfully";
    return true;
}

void QueueManager::clearQueue()
{
    m_waitingQueue.clear();
    m_hasCurrentTicket = false;
    stopExpireTimer();
}

void QueueManager::insertTicketByPriority(const Ticket& ticket)
{
    // VIP票据插入队首，普通票据插入队尾
    if (ticket.getUserType() == Ticket::VIP) {
        // 找到第一个非VIP票据的位置
        int insertPos = 0;
        for (int i = 0; i < m_waitingQueue.size(); ++i) {
            if (m_waitingQueue.at(i).getUserType() != Ticket::VIP) {
                break;
            }
            insertPos++;
        }
        m_waitingQueue.insert(insertPos, ticket);
    } else {
        // 普通票据直接添加到队尾
        m_waitingQueue.append(ticket);
    }
}

void QueueManager::removeTicketFromQueue(const Ticket& ticket)
{
    for (int i = 0; i < m_waitingQueue.size(); ++i) {
        if (m_waitingQueue.at(i) == ticket) {
            m_waitingQueue.removeAt(i);
            break;
        }
    }
}

int& QueueManager::getCounterRef(Ticket::UserType userType, Ticket::BusinessType businessType)
{
    if (userType == Ticket::VIP) {
        if (businessType == Ticket::SAVINGS) {
            return m_vipSavingsCount;
        } else {
            return m_vipConsultingCount;
        }
    } else {
        if (businessType == Ticket::SAVINGS) {
            return m_normalSavingsCount;
        } else {
            return m_normalConsultingCount;
        }
    }
}

void QueueManager::initCounters()
{
    m_normalSavingsCount = 0;
    m_normalConsultingCount = 0;
    m_vipSavingsCount = 0;
    m_vipConsultingCount = 0;
}

void QueueManager::startExpireTimer()
{
    if (!m_expireTimer->isActive()) {
        m_expireTimer->start();
    }
}

void QueueManager::stopExpireTimer()
{
    if (m_expireTimer->isActive()) {
        m_expireTimer->stop();
    }
}

void QueueManager::checkExpiredTickets()
{
    QMutexLocker locker(&m_mutex);

    if (!m_hasCurrentTicket) {
        stopExpireTimer();
        return;
    }

    // 检查当前票据是否超时（10秒无响应视为超时）
    QDateTime now = QDateTime::currentDateTime();
    if (m_currentTicket.getCallTime().isValid() &&
        m_currentTicket.getCallTime().secsTo(now) > 10) {

        if (m_currentTicket.getCallCount() >= 3) {
            // 超过最大呼叫次数，过期
            expireCurrent();
        }
    }
}
