# 公共库项目文件
# 适用于Qt 4.8.5

TEMPLATE = lib
CONFIG += staticlib
TARGET = common

# Qt模块
QT += core network
QT -= gui

# 编译器设置
QMAKE_CXXFLAGS += -std=c++98  # 避免使用C++11特性

# 源文件
SOURCES += \
    ticket.cpp \
    queue_manager.cpp \
    message_protocol.cpp \
    network_client.cpp \
    data_manager.cpp

# 头文件
HEADERS += \
    ticket.h \
    queue_manager.h \
    message_protocol.h \
    network_client.h \
    data_manager.h

# 输出目录
DESTDIR = ../lib
OBJECTS_DIR = ../build/common
MOC_DIR = ../build/common

# 安装规则
target.path = ../lib
headers.path = ../include
headers.files = $$HEADERS
INSTALLS += target headers
