#ifndef NETWORK_CLIENT_H
#define NETWORK_CLIENT_H

#include "message_protocol.h"
#include <QObject>
#include <QTcpSocket>
#include <QTimer>
#include <QHostAddress>

/**
 * 网络客户端类
 * 负责与其他客户端的TCP通信
 */
class NetworkClient : public QObject
{
    Q_OBJECT

public:
    enum ConnectionState {
        DISCONNECTED = 0,
        CONNECTING = 1,
        CONNECTED = 2,
        ERROR = 3
    };

public:
    explicit NetworkClient(QObject *parent = 0);
    ~NetworkClient();
    
    // 连接管理
    void connectToHost(const QString& hostAddress, quint16 port);
    void disconnectFromHost();
    bool isConnected() const;
    ConnectionState getConnectionState() const;
    
    // 客户端注册
    void registerClient(MessageProtocol::ClientType clientType, int clientId, const QString& clientName);
    void unregisterClient();
    
    // 消息发送
    bool sendMessage(const QString& message);
    bool sendTakeNumberRequest(Ticket::UserType userType, Ticket::BusinessType businessType);
    bool sendCallNextRequest(int windowId);
    bool sendCallCurrentRequest(int windowId);
    bool sendFinishCurrentRequest(int windowId);
    bool sendQueueStatusRequest();
    bool sendHeartbeat();
    
    // 广播消息发送
    bool broadcastTicketAdded(const Ticket& ticket);
    bool broadcastTicketCalled(const Ticket& ticket, int windowId);
    bool broadcastTicketFinished(const Ticket& ticket, int windowId);
    bool broadcastTicketExpired(const Ticket& ticket, int windowId);
    bool broadcastQueueChanged();
    
    // 设置客户端信息
    void setClientInfo(MessageProtocol::ClientType clientType, int clientId, const QString& clientName);
    
    // 心跳设置
    void setHeartbeatInterval(int intervalMs);
    void startHeartbeat();
    void stopHeartbeat();

signals:
    // 连接状态信号
    void connected();
    void disconnected();
    void connectionError(const QString& error);
    void stateChanged(ConnectionState state);
    
    // 消息接收信号
    void messageReceived(const QString& message);
    
    // 请求响应信号
    void takeNumberResponse(const Ticket& ticket, bool success, const QString& error);
    void callNextResponse(const Ticket& ticket, bool success, const QString& error);
    void callCurrentResponse(bool success, const QString& error);
    void finishCurrentResponse(bool success, const QString& error);
    void queueStatusResponse(int waitingCount, const QList<Ticket>& tickets);
    
    // 广播消息信号
    void ticketAddedBroadcast(const Ticket& ticket);
    void ticketCalledBroadcast(const Ticket& ticket, int windowId);
    void ticketFinishedBroadcast(const Ticket& ticket, int windowId);
    void ticketExpiredBroadcast(const Ticket& ticket, int windowId);
    void queueChangedBroadcast();
    
    // 客户端管理信号
    void clientRegistered(MessageProtocol::ClientType clientType, int clientId, const QString& clientName);
    void clientUnregistered(MessageProtocol::ClientType clientType, int clientId);
    void heartbeatReceived(MessageProtocol::ClientType clientType, int clientId);
    
    // 错误信号
    void errorResponse(const QString& error);

private slots:
    void onConnected();
    void onDisconnected();
    void onError(QAbstractSocket::SocketError error);
    void onReadyRead();
    void onHeartbeatTimeout();

private:
    QTcpSocket* m_socket;
    ConnectionState m_state;
    QString m_receiveBuffer;
    
    // 客户端信息
    MessageProtocol::ClientType m_clientType;
    int m_clientId;
    QString m_clientName;
    bool m_registered;
    
    // 心跳
    QTimer* m_heartbeatTimer;
    int m_heartbeatInterval;
    
    void setState(ConnectionState state);
    void processMessage(const QString& message);
    void handleTakeNumberResponse(const QStringList& params);
    void handleCallNextResponse(const QStringList& params);
    void handleCallCurrentResponse(const QStringList& params);
    void handleFinishCurrentResponse(const QStringList& params);
    void handleQueueStatusResponse(const QStringList& params);
    void handleTicketAddedBroadcast(const QStringList& params);
    void handleTicketCalledBroadcast(const QStringList& params);
    void handleTicketFinishedBroadcast(const QStringList& params);
    void handleTicketExpiredBroadcast(const QStringList& params);
    void handleQueueChangedBroadcast(const QStringList& params);
    void handleClientRegister(const QStringList& params);
    void handleClientUnregister(const QStringList& params);
    void handleHeartbeat(const QStringList& params);
    void handleErrorResponse(const QStringList& params);
    
    QString extractCompleteMessage();
    bool hasCompleteMessage() const;
};

#endif // NETWORK_CLIENT_H
