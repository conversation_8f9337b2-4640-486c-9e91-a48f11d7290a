#ifndef TICKET_H
#define TICKET_H

#include <QString>
#include <QDateTime>

/**
 * 号码票据类
 * 格式：2位字母+3位数字，如"VA001"
 * 首字母：C=普通用户，V=VIP用户
 * 次字母：A=个人储蓄业务，B=咨询业务
 * 后三位：业务序号，每天从001开始
 */
class Ticket
{
public:
    enum UserType {
        NORMAL = 0,  // 普通用户 (C)
        VIP = 1      // VIP用户 (V)
    };
    
    enum BusinessType {
        SAVINGS = 0,    // 个人储蓄业务 (A)
        CONSULTING = 1  // 咨询业务 (B)
    };
    
    enum TicketStatus {
        WAITING = 0,    // 等待中
        CALLING = 1,    // 正在呼叫
        PROCESSING = 2, // 正在办理
        FINISHED = 3,   // 已完成
        EXPIRED = 4     // 已过期
    };

public:
    Ticket();
    Ticket(UserType userType, BusinessType businessType, int number);
    Ticket(const QString& ticketNumber);
    
    // 获取完整号码字符串
    QString getTicketNumber() const;
    
    // 获取显示文本
    QString getDisplayText() const;
    
    // 获取业务类型文本
    QString getBusinessTypeText() const;
    
    // 获取用户类型文本
    QString getUserTypeText() const;
    
    // 属性访问
    UserType getUserType() const { return m_userType; }
    BusinessType getBusinessType() const { return m_businessType; }
    int getNumber() const { return m_number; }
    TicketStatus getStatus() const { return m_status; }
    QDateTime getCreateTime() const { return m_createTime; }
    QDateTime getCallTime() const { return m_callTime; }
    int getCallCount() const { return m_callCount; }
    
    // 状态设置
    void setStatus(TicketStatus status);
    void setCallTime(const QDateTime& time);
    void incrementCallCount();
    
    // 比较操作（用于排序）
    bool operator<(const Ticket& other) const;
    bool operator==(const Ticket& other) const;
    
    // 序列化
    QString toString() const;
    bool fromString(const QString& str);
    
    // 静态方法
    static bool isValidTicketNumber(const QString& ticketNumber);
    static UserType parseUserType(const QString& ticketNumber);
    static BusinessType parseBusinessType(const QString& ticketNumber);
    static int parseNumber(const QString& ticketNumber);

private:
    UserType m_userType;
    BusinessType m_businessType;
    int m_number;
    TicketStatus m_status;
    QDateTime m_createTime;
    QDateTime m_callTime;
    int m_callCount;  // 呼叫次数
    
    void init();
    char getUserTypeChar() const;
    char getBusinessTypeChar() const;
};

#endif // TICKET_H
