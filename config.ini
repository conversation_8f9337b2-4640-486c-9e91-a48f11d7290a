# 银行取号叫号系统配置文件

[Network]
# 服务器地址（用于客户端连接）
ServerAddress=127.0.0.1
ServerPort=8888

# 心跳间隔（毫秒）
HeartbeatInterval=30000

# 连接超时（毫秒）
ConnectionTimeout=5000

[Data]
# 数据文件路径
DataFilePath=data/queue_data.txt

# 备份目录
BackupPath=data/backup/

# 自动保存间隔（毫秒）
AutoSaveInterval=30000

# 是否启用自动保存
AutoSaveEnabled=true

[Queue]
# 最大呼叫次数
MaxCallCount=3

# 呼叫间隔（毫秒）
CallInterval=10000

# 票据过期时间（毫秒）
TicketExpireTime=300000

[UI]
# 界面更新间隔（毫秒）
DisplayUpdateInterval=1000

# 状态查询间隔（毫秒）
StatusQueryInterval=5000

# 字体大小
FontSize=12

# 是否启用中文界面
ChineseUI=true

[System]
# 系统版本
Version=1.0.0

# 调试模式
DebugMode=false

# 日志级别（0=关闭, 1=错误, 2=警告, 3=信息, 4=调试）
LogLevel=2

# 日志文件路径
LogFilePath=logs/system.log
