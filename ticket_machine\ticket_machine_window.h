#ifndef TICKET_MACHINE_WINDOW_H
#define TICKET_MACHINE_WINDOW_H

#include "../common/ticket.h"
#include "../common/queue_manager.h"
#include "../common/network_client.h"
#include <QMainWindow>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QTextEdit>
#include <QTimer>
#include <QGroupBox>

/**
 * 取号机主窗口
 * 提供取号功能和队列状态显示
 */
class TicketMachineWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit TicketMachineWindow(QWidget *parent = 0);
    ~TicketMachineWindow();

private slots:
    // 取号按钮槽函数
    void onTakeNormalSavings();
    void onTakeNormalConsulting();
    void onTakeVipSavings();
    void onTakeVipConsulting();
    
    // 网络相关槽函数
    void onNetworkConnected();
    void onNetworkDisconnected();
    void onNetworkError(const QString& error);
    void onTakeNumberResponse(const Ticket& ticket, bool success, const QString& error);
    void onQueueStatusResponse(int waitingCount, const QList<Ticket>& tickets);
    
    // 广播消息槽函数
    void onTicketAddedBroadcast(const Ticket& ticket);
    void onTicketCalledBroadcast(const Ticket& ticket, int windowId);
    void onTicketFinishedBroadcast(const Ticket& ticket, int windowId);
    void onTicketExpiredBroadcast(const Ticket& ticket, int windowId);
    void onQueueChangedBroadcast();
    
    // 定时器槽函数
    void onUpdateDisplay();
    void onRequestQueueStatus();
    void onConnectionTimeout();

private:
    // UI组件
    QWidget* m_centralWidget;
    QVBoxLayout* m_mainLayout;
    
    // 标题区域
    QLabel* m_titleLabel;
    QLabel* m_timeLabel;
    
    // 取号按钮区域
    QGroupBox* m_buttonGroup;
    QGridLayout* m_buttonLayout;
    QPushButton* m_normalSavingsBtn;
    QPushButton* m_normalConsultingBtn;
    QPushButton* m_vipSavingsBtn;
    QPushButton* m_vipConsultingBtn;
    
    // 状态显示区域
    QGroupBox* m_statusGroup;
    QVBoxLayout* m_statusLayout;
    QLabel* m_connectionStatusLabel;
    QLabel* m_totalWaitingLabel;
    QLabel* m_normalSavingsWaitingLabel;
    QLabel* m_normalConsultingWaitingLabel;
    QLabel* m_vipSavingsWaitingLabel;
    QLabel* m_vipConsultingWaitingLabel;
    
    // 最近取号记录区域
    QGroupBox* m_recentGroup;
    QTextEdit* m_recentTicketsText;
    
    // 当前叫号显示区域
    QGroupBox* m_currentGroup;
    QLabel* m_currentTicketLabel;
    QLabel* m_currentWindowLabel;
    
    // 网络和业务逻辑
    NetworkClient* m_networkClient;
    QueueManager* m_queueManager;
    
    // 定时器
    QTimer* m_displayTimer;      // 界面更新定时器
    QTimer* m_statusTimer;       // 状态查询定时器
    
    // 状态变量
    bool m_networkConnected;
    int m_totalWaiting;
    int m_normalSavingsWaiting;
    int m_normalConsultingWaiting;
    int m_vipSavingsWaiting;
    int m_vipConsultingWaiting;
    QString m_currentTicket;
    int m_currentWindow;
    QString m_lastExpiredTicket;  // 最后一个超时的票据
    QDateTime m_expiredTime;      // 超时时间
    QList<Ticket> m_recentTickets;
    
    // 初始化函数
    void initUI();
    void initNetwork();
    void initTimers();
    void setupConnections();
    
    // UI更新函数
    void updateTimeDisplay();
    void updateConnectionStatus();
    void updateWaitingCounts();
    void updateRecentTickets();
    void updateCurrentTicket();
    void updateButtonStates();
    
    // 业务逻辑函数
    void takeNumber(Ticket::UserType userType, Ticket::BusinessType businessType);
    void addRecentTicket(const Ticket& ticket);

private slots:
    // 按钮槽函数
    void onTakeNormalSavings();
    void onTakeNormalConsulting();
    void onTakeVipSavings();
    void onTakeVipConsulting();
    void onUpdateDisplay();
    void onRequestQueueStatus();
    void onConnectionTimeout();

    // 网络连接槽函数
    void connectToServer();
    void onNetworkConnected();
    void onNetworkDisconnected();
    void onNetworkError(const QString& error);
    void onTakeNumberResponse(const Ticket& ticket, bool success, const QString& error);
    void onQueueStatusResponse(int waitingCount, const QList<Ticket>& tickets);

    // 广播消息槽函数
    void onTicketAddedBroadcast(const Ticket& ticket);
    void onTicketCalledBroadcast(const Ticket& ticket, int windowId);
    void onTicketFinishedBroadcast(const Ticket& ticket, int windowId);
    void onTicketExpiredBroadcast(const Ticket& ticket, int windowId);
    void onQueueChangedBroadcast();

    // 工具函数
    QString formatTime() const;
    QString getBusinessTypeText(Ticket::BusinessType businessType) const;
    QString getUserTypeText(Ticket::UserType userType) const;
    QString getBusinessDescription(const QString& ticketNumber) const;
    void showMessage(const QString& title, const QString& message);
    void showTicket(const Ticket& ticket);
};

#endif // TICKET_MACHINE_WINDOW_H
