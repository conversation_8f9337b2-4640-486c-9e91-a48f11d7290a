#!/bin/bash

# 立即编译脚本 - 解决当前编译问题
echo "=== 立即编译银行取号叫号系统 ==="
echo ""

# 显示当前状态
echo "当前目录内容:"
ls -la
echo ""

# 清理之前的编译结果
echo "1. 清理之前的编译结果..."
make clean 2>/dev/null || true
rm -rf bin lib build
rm -f Makefile */Makefile
echo "   清理完成"
echo ""

# 创建必要目录
echo "2. 创建必要目录..."
mkdir -p bin lib build
mkdir -p bin/data
mkdir -p bin/data/backup
echo "   目录创建完成"
echo ""

# 编译公共库
echo "3. 编译公共库..."
cd common
echo "   当前目录: $(pwd)"
echo "   运行: qmake"
qmake
if [ $? -ne 0 ]; then
    echo "   ❌ qmake失败"
    cd ..
    exit 1
fi

echo "   运行: make"
make
if [ $? -ne 0 ]; then
    echo "   ❌ 公共库编译失败"
    cd ..
    exit 1
fi
echo "   ✅ 公共库编译成功"
cd ..
echo ""

# 检查公共库是否生成
if [ -f "lib/libcommon.a" ]; then
    echo "   ✅ 公共库文件已生成: lib/libcommon.a"
    ls -la lib/libcommon.a
else
    echo "   ❌ 公共库文件未生成"
    exit 1
fi
echo ""

# 编译取号机
echo "4. 编译取号机..."
cd ticket_machine
echo "   当前目录: $(pwd)"
echo "   运行: qmake"
qmake
if [ $? -ne 0 ]; then
    echo "   ❌ qmake失败"
    cd ..
    exit 1
fi

echo "   运行: make"
make
if [ $? -ne 0 ]; then
    echo "   ❌ 取号机编译失败"
    cd ..
    exit 1
fi
echo "   ✅ 取号机编译成功"
cd ..
echo ""

# 检查取号机是否生成
if [ -f "bin/ticket_machine" ]; then
    echo "   ✅ 取号机文件已生成: bin/ticket_machine"
    ls -la bin/ticket_machine
else
    echo "   ❌ 取号机文件未生成"
    exit 1
fi
echo ""

# 编译叫号机
echo "5. 编译叫号机..."
cd calling_machine
echo "   当前目录: $(pwd)"
echo "   运行: qmake"
qmake
if [ $? -ne 0 ]; then
    echo "   ❌ qmake失败"
    cd ..
    exit 1
fi

echo "   运行: make"
make
if [ $? -ne 0 ]; then
    echo "   ❌ 叫号机编译失败"
    cd ..
    exit 1
fi
echo "   ✅ 叫号机编译成功"
cd ..
echo ""

# 检查叫号机是否生成
if [ -f "bin/calling_machine" ]; then
    echo "   ✅ 叫号机文件已生成: bin/calling_machine"
    ls -la bin/calling_machine
else
    echo "   ❌ 叫号机文件未生成"
    exit 1
fi
echo ""

# 最终检查
echo "6. 最终检查..."
echo "生成的文件:"
echo ""
echo "📁 bin/ 目录:"
ls -la bin/
echo ""
echo "📁 lib/ 目录:"
ls -la lib/
echo ""

# 验证文件
echo "文件验证:"
if [ -f "lib/libcommon.a" ]; then
    echo "   ✅ libcommon.a - $(file lib/libcommon.a)"
fi

if [ -f "bin/ticket_machine" ]; then
    echo "   ✅ ticket_machine - $(file bin/ticket_machine)"
fi

if [ -f "bin/calling_machine" ]; then
    echo "   ✅ calling_machine - $(file bin/calling_machine)"
fi

echo ""
echo "🎉 编译完成!"
echo ""
echo "运行方法:"
echo "   ./bin/ticket_machine                # 启动取号机"
echo "   ./bin/calling_machine 1             # 启动1号窗口叫号机"
echo "   ./bin/calling_machine 2             # 启动2号窗口叫号机"
echo ""
echo "测试方法:"
echo "   chmod +x test_system.sh"
echo "   ./test_system.sh"
