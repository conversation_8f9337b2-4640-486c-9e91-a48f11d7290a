#!/bin/bash

# 快速测试编译脚本
echo "=== 快速编译测试 ==="

# 检查Qt版本
echo "Qt版本检查:"
qmake -version
echo ""

# 清理
echo "清理之前的编译结果..."
make clean 2>/dev/null || true
rm -f Makefile

# 生成Makefile
echo "生成Makefile..."
qmake bank_queue_system.pro

if [ $? -ne 0 ]; then
    echo "❌ qmake失败"
    exit 1
fi

# 编译（只编译common库进行快速测试）
echo "编译common库..."
cd common
make

if [ $? -eq 0 ]; then
    echo "✅ common库编译成功"
    cd ..
    
    # 尝试编译完整项目
    echo "编译完整项目..."
    make
    
    if [ $? -eq 0 ]; then
        echo "✅ 完整项目编译成功!"
        echo ""
        echo "生成的文件:"
        ls -la bin/ 2>/dev/null || echo "bin目录不存在"
        ls -la lib/ 2>/dev/null || echo "lib目录不存在"
    else
        echo "❌ 完整项目编译失败"
        echo "但common库编译成功，说明基本修复有效"
    fi
else
    echo "❌ common库编译失败"
    cd ..
fi

echo ""
echo "=== 测试完成 ==="
