#include "ticket_machine_window.h"
#include <QApplication>
#include <QTextCodec>
#include <QDir>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("BankTicketMachine");
    app.setApplicationVersion("1.0");
    app.setOrganizationName("BankQueueSystem");
    
    // 设置中文编码支持 (Qt 4.8.5)
    QTextCodec::setCodecForTr(QTextCodec::codecForName("UTF-8"));
    QTextCodec::setCodecForCStrings(QTextCodec::codecForName("UTF-8"));
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
    
    // 确保数据目录存在
    QDir dataDir;
    if (!dataDir.exists("data")) {
        dataDir.mkdir("data");
    }
    
    // 创建并显示主窗口
    TicketMachineWindow window;
    window.show();
    
    return app.exec();
}
