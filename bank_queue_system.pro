# 银行取号叫号系统主项目文件
# 适用于Qt 4.8.5

TEMPLATE = subdirs

# 子项目
SUBDIRS += \
    common \
    ticket_machine \
    calling_machine

# 设置子项目依赖关系
ticket_machine.depends = common
calling_machine.depends = common

# 项目配置
CONFIG += ordered

# 输出目录
DESTDIR = bin

# 项目信息
TARGET = BankQueueSystem
VERSION = 1.0.0

# 编译器设置
QMAKE_CXXFLAGS += -std=c++98  # 避免使用C++11特性

# 清理规则
QMAKE_CLEAN += -r bin
QMAKE_CLEAN += -r build
