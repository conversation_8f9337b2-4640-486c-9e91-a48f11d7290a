#include <QApplication>
#include <QMainWindow>
#include <QLabel>
#include <QVBoxLayout>
#include <QWidget>
#include <QPushButton>
#include <QDebug>

class SimpleCallingWindow : public QMainWindow
{
    Q_OBJECT

public:
    SimpleCallingWindow(QWidget *parent = 0) : QMainWindow(parent)
    {
        qDebug() << "Creating simple calling window...";
        
        setWindowTitle("Simple Bank Calling System");
        setMinimumSize(400, 300);
        
        QWidget* central = new QWidget(this);
        setCentralWidget(central);
        
        QVBoxLayout* layout = new QVBoxLayout(central);
        
        QLabel* title = new QLabel("Bank Calling System", this);
        title->setAlignment(Qt::AlignCenter);
        
        QPushButton* testBtn = new QPushButton("Test Button", this);
        
        layout->addWidget(title);
        layout->addWidget(testBtn);
        
        qDebug() << "Simple calling window created successfully";
    }
};

#include "main_simple.moc"

int main(int argc, char *argv[])
{
    qDebug() << "Starting simple calling machine...";
    QApplication app(argc, argv);
    qDebug() << "QApplication created";
    
    SimpleCallingWindow window;
    qDebug() << "Window created, showing...";
    window.show();
    qDebug() << "Window shown, starting event loop...";
    
    return app.exec();
}
