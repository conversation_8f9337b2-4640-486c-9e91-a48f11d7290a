# 银行取号叫号系统

基于Qt 4.8.5开发的银行取号叫号系统，采用C/S架构，支持多客户端网络通信。

## 系统概述

### 功能特点
- **取号功能**: 支持普通用户和VIP用户取号
- **叫号功能**: 按优先级自动叫号，支持重复呼叫
- **网络通信**: 客户端间实时数据同步
- **数据持久化**: 支持断电恢复，每日自动重置
- **界面友好**: 直观的图形界面，支持触摸屏操作

### 系统架构
- **硬件部署**: 3个开发板通过路由器连接
  - 1个取号机
  - 2个叫号机（1号窗口、2号窗口）
- **软件架构**: C/S模式，独立客户端通过TCP网络通信

## 技术规格

### 开发环境
- **操作系统**: Ubuntu 18.04
- **Qt版本**: 4.8.5
- **编程语言**: C++（避免C++11特性）
- **编译器**: GCC 7.5.0+

### 核心数据结构

#### 号码格式
- **格式**: 2位字母 + 3位数字（如"VA001"）
- **首字母**: C=普通用户，V=VIP用户
- **次字母**: A=个人储蓄业务，B=咨询业务
- **后三位**: 业务序号，每天从001开始

#### 排队规则
- 唯一队列，VIP号码插入队首，普通号码插入队尾
- 同一号码最多重复呼叫3次（间隔10秒）
- 超过呼叫次数自动作废

## 编译安装

### 环境准备
```bash
# Ubuntu 18.04安装Qt 4.8.5
sudo apt-get update
sudo apt-get install qt4-dev-tools libqt4-dev libqt4-core libqt4-gui libqt4-network

# 验证安装
qmake -version
```

### 编译方法

#### 方法1: 使用Qt 4.8.5专用脚本（推荐）
```bash
chmod +x build_qt4.sh
./build_qt4.sh
```

#### 方法2: 使用通用编译脚本
```bash
chmod +x build.sh
./build.sh
```

#### 方法3: 手动编译
```bash
# 使用qmake
qmake bank_queue_system.pro
make clean
make -j$(nproc)

# 或使用cmake
mkdir build && cd build
cmake ..
make -j$(nproc)
```

#### 重要提示
- **已修复Qt 4.8.5兼容性问题**，详见 `QT4_FIXES.md`
- 推荐使用 `build_qt4.sh` 脚本，包含Qt版本检查和错误诊断

### 运行程序
```bash
# 取号机
./bin/ticket_machine

# 叫号机（指定窗口编号）
./bin/calling_machine 1  # 1号窗口
./bin/calling_machine 2  # 2号窗口
```

## 使用说明

### 取号机操作
1. **选择业务类型**
   - 普通储蓄业务（CA系列）
   - 普通咨询业务（CB系列）
   - VIP储蓄业务（VA系列）
   - VIP咨询业务（VB系列）

2. **界面显示**
   - 当前等待人数统计
   - 最近取号记录
   - 当前叫号信息
   - 网络连接状态

### 叫号机操作
1. **叫号控制**
   - **叫下一号**: 按优先级呼叫下一位客户
   - **重复呼叫**: 重复呼叫当前号码（最多3次）
   - **完成办理**: 完成当前客户业务
   - **重置窗口**: 重置窗口状态

2. **信息显示**
   - LCD数字显示当前号码
   - 队列状态统计
   - 等待队列详情
   - 操作记录日志

## 网络配置

### 默认设置
- **服务器地址**: 127.0.0.1（本地测试）
- **端口**: 8888
- **心跳间隔**: 30秒

### 生产环境配置
修改源码中的连接地址：
```cpp
// 在 connectToServer() 函数中修改
m_networkClient->connectToHost("*************", 8888);
```

## 数据存储

### 文件结构
```
bin/
├── data/
│   ├── queue_data.txt      # 队列数据
│   └── backup/             # 备份文件
│       ├── queue_data_20231201_120000.txt
│       └── ...
```

### 数据持久化
- **自动保存**: 每30秒自动保存
- **断电恢复**: 启动时自动加载上次数据
- **每日重置**: 每天自动清空队列，序号重置
- **备份机制**: 重要操作前自动备份

## 故障排除

### 常见问题

1. **编译错误**
   ```bash
   # 检查Qt版本（必须是4.8.5）
   qmake -version

   # 使用专用编译脚本
   ./build_qt4.sh

   # 手动清理重新编译
   make clean
   qmake
   make
   ```

2. **Qt 4.8.5兼容性问题**
   - 查看 `QT4_FIXES.md` 了解已修复的问题
   - 确保使用了修复后的代码
   - 避免使用C++11特性

3. **网络连接失败**
   - 检查网络配置
   - 确认防火墙设置
   - 验证端口是否被占用

4. **界面显示异常**
   - 检查字体支持
   - 确认中文编码设置
   - 验证Qt GUI模块

### 调试模式
```bash
# 启用调试输出
export QT_LOGGING_RULES="*.debug=true"
./bin/ticket_machine
```

### Qt 4.8.5特定问题
- **编译警告**: 已修复所有Qt 4.8.5兼容性警告
- **运行时错误**: 确保正确设置了UTF-8编码
- **信号槽连接**: 使用传统的SIGNAL/SLOT宏语法

## 系统扩展

### 后续可扩展功能
1. **业务类型扩展**: 支持新增业务类型
2. **语音播报**: 对接开发板音频设备
3. **统计分析**: 业务量统计、等待时间分析
4. **界面优化**: 适配不同屏幕尺寸

### 开发指南
- 遵循Qt 4.8.5 API规范
- 避免使用C++11及以上特性
- 保持代码向后兼容性
- 注重内存管理和资源释放

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请联系开发团队。
