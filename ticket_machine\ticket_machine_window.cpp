#include "ticket_machine_window.h"
#include <QApplication>
#include <QMessageBox>
#include <QDateTime>
#include <QFont>
#include <QSplitter>
#include <QScrollBar>
#include <QDebug>

TicketMachineWindow::TicketMachineWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(0)
    , m_networkClient(0)
    , m_queueManager(0)
    , m_displayTimer(0)
    , m_statusTimer(0)
    , m_networkConnected(false)
    , m_totalWaiting(0)
    , m_normalSavingsWaiting(0)
    , m_normalConsultingWaiting(0)
    , m_vipSavingsWaiting(0)
    , m_vipConsultingWaiting(0)
    , m_currentWindow(0)
{
    setWindowTitle("Bank Ticket Machine");
    setMinimumSize(800, 600);

    initUI();
    initNetwork();
    initTimers();
    setupConnections();

    // 连接到服务器
    connectToServer();
}

TicketMachineWindow::~TicketMachineWindow()
{
    if (m_networkClient) {
        m_networkClient->disconnectFromHost();
    }
}

void TicketMachineWindow::initUI()
{
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_mainLayout->setSpacing(10);
    m_mainLayout->setMargin(10);
    
    // 标题区域
    m_titleLabel = new QLabel("Welcome to Bank Queue System", this);
    QFont titleFont = m_titleLabel->font();
    titleFont.setPointSize(24);
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_titleLabel->setStyleSheet("QLabel { color: #2c3e50; padding: 10px; }");
    
    m_timeLabel = new QLabel(this);
    QFont timeFont = m_timeLabel->font();
    timeFont.setPointSize(14);
    m_timeLabel->setFont(timeFont);
    m_timeLabel->setAlignment(Qt::AlignCenter);
    m_timeLabel->setStyleSheet("QLabel { color: #34495e; }");
    
    // 取号按钮区域
    m_buttonGroup = new QGroupBox("Please Select Service Type", this);
    m_buttonLayout = new QGridLayout(m_buttonGroup);
    m_buttonLayout->setSpacing(15);
    
    // 创建取号按钮
    m_normalSavingsBtn = new QPushButton("Normal Savings\nDeposit & Withdrawal\n(CA Series)", this);
    m_normalConsultingBtn = new QPushButton("Normal Consulting\nAccount & Advisory\n(CB Series)", this);
    m_vipSavingsBtn = new QPushButton("VIP Savings\nDeposit & Withdrawal\n(VA Series)", this);
    m_vipConsultingBtn = new QPushButton("VIP Consulting\nAccount & Advisory\n(VB Series)", this);
    
    // 设置按钮样式和大小
    QList<QPushButton*> buttons;
    buttons.append(m_normalSavingsBtn);
    buttons.append(m_normalConsultingBtn);
    buttons.append(m_vipSavingsBtn);
    buttons.append(m_vipConsultingBtn);
    
    foreach (QPushButton* btn, buttons) {
        btn->setMinimumSize(220, 100);
        btn->setFont(QFont("Microsoft YaHei", 11, QFont::Bold));
        btn->setStyleSheet(
            "QPushButton {"
            "    background-color: #3498db;"
            "    color: white;"
            "    border: none;"
            "    border-radius: 8px;"
            "    padding: 10px;"
            "}"
            "QPushButton:hover {"
            "    background-color: #2980b9;"
            "}"
            "QPushButton:pressed {"
            "    background-color: #21618c;"
            "}"
            "QPushButton:disabled {"
            "    background-color: #bdc3c7;"
            "    color: #7f8c8d;"
            "}"
        );
    }
    
    // VIP按钮使用不同颜色
    m_vipSavingsBtn->setStyleSheet(
        "QPushButton {"
        "    background-color: #e74c3c;"
        "    color: white;"
        "    border: none;"
        "    border-radius: 8px;"
        "    padding: 10px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #c0392b;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #a93226;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #bdc3c7;"
        "    color: #7f8c8d;"
        "}"
    );
    
    m_vipConsultingBtn->setStyleSheet(m_vipSavingsBtn->styleSheet());
    
    // 布局按钮
    m_buttonLayout->addWidget(m_normalSavingsBtn, 0, 0);
    m_buttonLayout->addWidget(m_normalConsultingBtn, 0, 1);
    m_buttonLayout->addWidget(m_vipSavingsBtn, 1, 0);
    m_buttonLayout->addWidget(m_vipConsultingBtn, 1, 1);
    
    // 状态显示区域
    m_statusGroup = new QGroupBox("Queue Status", this);
    m_statusLayout = new QVBoxLayout(m_statusGroup);
    
    m_connectionStatusLabel = new QLabel("Connection Status: Disconnected", this);
    m_totalWaitingLabel = new QLabel("Total Waiting: 0", this);
    m_normalSavingsWaitingLabel = new QLabel("Normal Savings (CA) Waiting: 0", this);
    m_normalConsultingWaitingLabel = new QLabel("Normal Consulting (CB) Waiting: 0", this);
    m_vipSavingsWaitingLabel = new QLabel("VIP Savings (VA) Waiting: 0", this);
    m_vipConsultingWaitingLabel = new QLabel("VIP Consulting (VB) Waiting: 0", this);
    
    m_statusLayout->addWidget(m_connectionStatusLabel);
    m_statusLayout->addWidget(m_totalWaitingLabel);
    m_statusLayout->addWidget(m_normalSavingsWaitingLabel);
    m_statusLayout->addWidget(m_normalConsultingWaitingLabel);
    m_statusLayout->addWidget(m_vipSavingsWaitingLabel);
    m_statusLayout->addWidget(m_vipConsultingWaitingLabel);
    
    // 当前叫号显示区域
    m_currentGroup = new QGroupBox("Current Calling", this);
    QVBoxLayout* currentLayout = new QVBoxLayout(m_currentGroup);

    m_currentTicketLabel = new QLabel("No Current Call", this);
    m_currentWindowLabel = new QLabel("", this);
    
    QFont currentFont = m_currentTicketLabel->font();
    currentFont.setPointSize(16);
    currentFont.setBold(true);
    m_currentTicketLabel->setFont(currentFont);
    m_currentWindowLabel->setFont(currentFont);
    
    m_currentTicketLabel->setAlignment(Qt::AlignCenter);
    m_currentWindowLabel->setAlignment(Qt::AlignCenter);
    m_currentTicketLabel->setStyleSheet("QLabel { color: #e74c3c; }");
    m_currentWindowLabel->setStyleSheet("QLabel { color: #e74c3c; }");
    
    currentLayout->addWidget(m_currentTicketLabel);
    currentLayout->addWidget(m_currentWindowLabel);
    
    // 最近取号记录区域
    m_recentGroup = new QGroupBox("Recent Tickets", this);
    QVBoxLayout* recentLayout = new QVBoxLayout(m_recentGroup);
    
    m_recentTicketsText = new QTextEdit(this);
    m_recentTicketsText->setReadOnly(true);
    m_recentTicketsText->setMaximumHeight(150);
    m_recentTicketsText->setFont(QFont("Consolas", 10));
    
    recentLayout->addWidget(m_recentTicketsText);
    
    // 添加到主布局
    m_mainLayout->addWidget(m_titleLabel);
    m_mainLayout->addWidget(m_timeLabel);
    m_mainLayout->addWidget(m_buttonGroup);
    
    // 创建水平分割器放置状态和记录区域
    QSplitter* splitter = new QSplitter(Qt::Horizontal, this);
    splitter->addWidget(m_statusGroup);
    splitter->addWidget(m_currentGroup);
    splitter->setStretchFactor(0, 1);
    splitter->setStretchFactor(1, 1);
    
    m_mainLayout->addWidget(splitter);
    m_mainLayout->addWidget(m_recentGroup);
    
    // 设置布局比例
    m_mainLayout->setStretchFactor(m_buttonGroup, 2);
    m_mainLayout->setStretchFactor(splitter, 1);
    m_mainLayout->setStretchFactor(m_recentGroup, 1);
}

void TicketMachineWindow::initNetwork()
{
    m_networkClient = new NetworkClient(this);
    m_networkClient->setClientInfo(MessageProtocol::TICKET_MACHINE, 1, QString::fromUtf8("取号机-1"));

    m_queueManager = new QueueManager(this);

    // 尝试加载持久化数据
    m_queueManager->loadFromFile("data/queue_data.txt");
}

void TicketMachineWindow::initTimers()
{
    // 界面更新定时器 - 每秒更新一次
    m_displayTimer = new QTimer(this);
    m_displayTimer->setInterval(1000);
    m_displayTimer->start();

    // 状态查询定时器 - 每5秒查询一次
    m_statusTimer = new QTimer(this);
    m_statusTimer->setInterval(5000);
    m_statusTimer->start();
}

void TicketMachineWindow::setupConnections()
{
    // 按钮连接
    connect(m_normalSavingsBtn, SIGNAL(clicked()), this, SLOT(onTakeNormalSavings()));
    connect(m_normalConsultingBtn, SIGNAL(clicked()), this, SLOT(onTakeNormalConsulting()));
    connect(m_vipSavingsBtn, SIGNAL(clicked()), this, SLOT(onTakeVipSavings()));
    connect(m_vipConsultingBtn, SIGNAL(clicked()), this, SLOT(onTakeVipConsulting()));

    // 定时器连接
    connect(m_displayTimer, SIGNAL(timeout()), this, SLOT(onUpdateDisplay()));
    connect(m_statusTimer, SIGNAL(timeout()), this, SLOT(onRequestQueueStatus()));
}

// 网络相关函数已移除 - 仅支持离线模式

void TicketMachineWindow::onTakeNormalSavings()
{
    takeNumber(Ticket::NORMAL, Ticket::SAVINGS);
}

void TicketMachineWindow::onTakeNormalConsulting()
{
    takeNumber(Ticket::NORMAL, Ticket::CONSULTING);
}

void TicketMachineWindow::onTakeVipSavings()
{
    takeNumber(Ticket::VIP, Ticket::SAVINGS);
}

void TicketMachineWindow::onTakeVipConsulting()
{
    takeNumber(Ticket::VIP, Ticket::CONSULTING);
}

void TicketMachineWindow::takeNumber(Ticket::UserType userType, Ticket::BusinessType businessType)
{
    // 离线模式：使用本地队列管理器
    Ticket ticket = m_queueManager->takeNumber(userType, businessType);
    addRecentTicket(ticket);
    showTicket(ticket);

    // 保存数据
    m_queueManager->saveToFile("data/queue_data.txt");

    qDebug() << "Ticket issued (offline):" << ticket.getDisplayText();
}

// 网络相关函数已移除 - 仅支持离线模式

// 网络响应函数已移除 - 仅支持离线模式

void TicketMachineWindow::onQueueStatusResponse(int waitingCount, const QList<Ticket>& tickets)
{
    Q_UNUSED(tickets);

    m_totalWaiting = waitingCount;

    // 统计各类型等待人数
    m_normalSavingsWaiting = 0;
    m_normalConsultingWaiting = 0;
    m_vipSavingsWaiting = 0;
    m_vipConsultingWaiting = 0;

    for (int i = 0; i < tickets.size(); ++i) {
        const Ticket& ticket = tickets.at(i);
        if (ticket.getUserType() == Ticket::NORMAL) {
            if (ticket.getBusinessType() == Ticket::SAVINGS) {
                m_normalSavingsWaiting++;
            } else {
                m_normalConsultingWaiting++;
            }
        } else {
            if (ticket.getBusinessType() == Ticket::SAVINGS) {
                m_vipSavingsWaiting++;
            } else {
                m_vipConsultingWaiting++;
            }
        }
    }

    updateWaitingCounts();
}

void TicketMachineWindow::onTicketAddedBroadcast(const Ticket& ticket)
{
    addRecentTicket(ticket);

    // 更新等待计数
    if (ticket.getUserType() == Ticket::NORMAL) {
        if (ticket.getBusinessType() == Ticket::SAVINGS) {
            m_normalSavingsWaiting++;
        } else {
            m_normalConsultingWaiting++;
        }
    } else {
        if (ticket.getBusinessType() == Ticket::SAVINGS) {
            m_vipSavingsWaiting++;
        } else {
            m_vipConsultingWaiting++;
        }
    }

    m_totalWaiting++;
    updateWaitingCounts();
}

void TicketMachineWindow::onTicketCalledBroadcast(const Ticket& ticket, int windowId)
{
    m_currentTicket = ticket.getDisplayText();
    m_currentWindow = windowId;
    updateCurrentTicket();

    // 减少等待计数
    if (ticket.getUserType() == Ticket::NORMAL) {
        if (ticket.getBusinessType() == Ticket::SAVINGS) {
            m_normalSavingsWaiting = qMax(0, m_normalSavingsWaiting - 1);
        } else {
            m_normalConsultingWaiting = qMax(0, m_normalConsultingWaiting - 1);
        }
    } else {
        if (ticket.getBusinessType() == Ticket::SAVINGS) {
            m_vipSavingsWaiting = qMax(0, m_vipSavingsWaiting - 1);
        } else {
            m_vipConsultingWaiting = qMax(0, m_vipConsultingWaiting - 1);
        }
    }

    m_totalWaiting = qMax(0, m_totalWaiting - 1);
    updateWaitingCounts();
}

void TicketMachineWindow::onTicketFinishedBroadcast(const Ticket& ticket, int windowId)
{
    Q_UNUSED(ticket);
    Q_UNUSED(windowId);

    // 清空当前叫号显示
    m_currentTicket.clear();
    m_currentWindow = 0;
    updateCurrentTicket();
}

void TicketMachineWindow::onTicketExpiredBroadcast(const Ticket& ticket, int windowId)
{
    Q_UNUSED(ticket);
    Q_UNUSED(windowId);

    // 清空当前叫号显示
    m_currentTicket.clear();
    m_currentWindow = 0;
    updateCurrentTicket();
}

void TicketMachineWindow::onQueueChangedBroadcast()
{
    // 请求最新的队列状态
    if (m_networkConnected) {
        m_networkClient->sendQueueStatusRequest();
    }
}

void TicketMachineWindow::onUpdateDisplay()
{
    updateTimeDisplay();
}

void TicketMachineWindow::onRequestQueueStatus()
{
    // 统一使用本地队列管理器更新状态
    m_queueManager->loadFromFile("data/queue_data.txt");

    // 更新等待人数显示
    m_totalWaiting = m_queueManager->getWaitingCount();
    m_normalSavingsWaiting = m_queueManager->getWaitingCount(Ticket::NORMAL, Ticket::SAVINGS);
    m_normalConsultingWaiting = m_queueManager->getWaitingCount(Ticket::NORMAL, Ticket::CONSULTING);
    m_vipSavingsWaiting = m_queueManager->getWaitingCount(Ticket::VIP, Ticket::SAVINGS);
    m_vipConsultingWaiting = m_queueManager->getWaitingCount(Ticket::VIP, Ticket::CONSULTING);

    // 更新当前正在服务的票据
    QString previousTicket = m_currentTicket;
    if (m_queueManager->hasCurrentTicket()) {
        Ticket currentTicket = m_queueManager->getCurrentTicket();
        m_currentTicket = currentTicket.getTicketNumber(); // 显示票据号码（如 CA004）而不是描述文本
        m_currentWindow = 1; // 假设是1号窗口，实际应该从文件中读取
    } else {
        // 检测是否有票据超时
        if (!previousTicket.isEmpty() && m_currentTicket != previousTicket) {
            m_lastExpiredTicket = previousTicket;
            m_expiredTime = QDateTime::currentDateTime();
        }
        m_currentTicket = "";
        m_currentWindow = 0;
    }

    updateWaitingCounts();
    updateCurrentTicket();  // 更新当前叫号显示
}

void TicketMachineWindow::onConnectionTimeout()
{
    if (!m_networkConnected) {
        // 连接超时，切换到离线模式
        qDebug() << "Connection timeout - Running in offline mode";
        m_networkConnected = false;
        updateConnectionStatus();
        updateButtonStates();
    }
}

void TicketMachineWindow::updateTimeDisplay()
{
    m_timeLabel->setText(formatTime());
}

void TicketMachineWindow::updateConnectionStatus()
{
    m_connectionStatusLabel->setText("Connection Status: Offline Mode");
    m_connectionStatusLabel->setStyleSheet("QLabel { color: #3498db; }");
}

void TicketMachineWindow::updateWaitingCounts()
{
    m_totalWaitingLabel->setText(QString("Total Waiting: %1").arg(m_totalWaiting));
    m_normalSavingsWaitingLabel->setText(QString("Normal Savings (CA) Waiting: %1").arg(m_normalSavingsWaiting));
    m_normalConsultingWaitingLabel->setText(QString("Normal Consulting (CB) Waiting: %1").arg(m_normalConsultingWaiting));
    m_vipSavingsWaitingLabel->setText(QString("VIP Savings (VA) Waiting: %1").arg(m_vipSavingsWaiting));
    m_vipConsultingWaitingLabel->setText(QString("VIP Consulting (VB) Waiting: %1").arg(m_vipConsultingWaiting));
}

void TicketMachineWindow::updateRecentTickets()
{
    QString text;
    for (int i = m_recentTickets.size() - 1; i >= 0; --i) {
        const Ticket& ticket = m_recentTickets.at(i);
        text += QString("%1 - %2 - %3\n")
                .arg(ticket.getCreateTime().toString("hh:mm:ss"))
                .arg(ticket.getTicketNumber())
                .arg(ticket.getDisplayText());
    }

    m_recentTicketsText->setPlainText(text);

    // 滚动到底部
    QScrollBar* scrollBar = m_recentTicketsText->verticalScrollBar();
    scrollBar->setValue(scrollBar->maximum());
}

void TicketMachineWindow::updateCurrentTicket()
{
    if (m_currentTicket.isEmpty()) {
        // 检查是否有最近超时的票据
        if (!m_lastExpiredTicket.isEmpty() &&
            m_expiredTime.secsTo(QDateTime::currentDateTime()) < 30) { // 30秒内显示超时信息
            m_currentTicketLabel->setText(QString("Ticket %1 Expired").arg(m_lastExpiredTicket));
            m_currentWindowLabel->setText("Please take a new number if you need service");
        } else {
            m_currentTicketLabel->setText("No Current Call");
            m_currentWindowLabel->setText("All windows are available");
        }
    } else {
        // 获取业务类型描述
        QString businessDesc = getBusinessDescription(m_currentTicket);
        m_currentTicketLabel->setText(QString("Now Calling: %1").arg(m_currentTicket));
        m_currentWindowLabel->setText(QString("%1 - Please go to Window %2").arg(businessDesc).arg(m_currentWindow));
    }
}

void TicketMachineWindow::updateButtonStates()
{
    // 离线模式下所有按钮都可用
    m_normalSavingsBtn->setEnabled(true);
    m_normalConsultingBtn->setEnabled(true);
    m_vipSavingsBtn->setEnabled(true);
    m_vipConsultingBtn->setEnabled(true);
}

void TicketMachineWindow::addRecentTicket(const Ticket& ticket)
{
    m_recentTickets.append(ticket);

    // 只保留最近20条记录
    while (m_recentTickets.size() > 20) {
        m_recentTickets.removeFirst();
    }

    updateRecentTickets();
}

QString TicketMachineWindow::formatTime() const
{
    QDateTime now = QDateTime::currentDateTime();
    return now.toString("yyyy-MM-dd hh:mm:ss dddd");
}

QString TicketMachineWindow::getBusinessTypeText(Ticket::BusinessType businessType) const
{
    switch (businessType) {
    case Ticket::SAVINGS:
        return QString::fromUtf8("储蓄业务");
    case Ticket::CONSULTING:
        return QString::fromUtf8("咨询业务");
    default:
        return QString::fromUtf8("未知业务");
    }
}

QString TicketMachineWindow::getBusinessDescription(const QString& ticketNumber) const
{
    if (ticketNumber.length() >= 2) {
        QString prefix = ticketNumber.left(2);
        if (prefix == "CA") {
            return "Normal Savings";
        } else if (prefix == "CB") {
            return "Normal Consulting";
        } else if (prefix == "VA") {
            return "VIP Savings";
        } else if (prefix == "VB") {
            return "VIP Consulting";
        }
    }
    return "Unknown Service";
}

QString TicketMachineWindow::getUserTypeText(Ticket::UserType userType) const
{
    switch (userType) {
    case Ticket::NORMAL:
        return QString::fromUtf8("普通");
    case Ticket::VIP:
        return QString("VIP");
    default:
        return QString::fromUtf8("未知");
    }
}

void TicketMachineWindow::showMessage(const QString& title, const QString& message)
{
    QMessageBox::information(this, title, message);
}

void TicketMachineWindow::showTicket(const Ticket& ticket)
{
    QString businessDesc;
    if (ticket.getUserType() == Ticket::NORMAL) {
        if (ticket.getBusinessType() == Ticket::SAVINGS) {
            businessDesc = "Normal Savings (CA) - Deposit & Withdrawal";
        } else {
            businessDesc = "Normal Consulting (CB) - Account & Advisory";
        }
    } else {
        if (ticket.getBusinessType() == Ticket::SAVINGS) {
            businessDesc = "VIP Savings (VA) - Deposit & Withdrawal";
        } else {
            businessDesc = "VIP Consulting (VB) - Account & Advisory";
        }
    }

    QString message = QString("Ticket Issued Successfully!\n\nYour Number: %1\nService Type: %2\nCurrent Waiting: %3\n\nPlease keep your ticket and wait for your number to be called.\nGo to the designated window when called.")
                      .arg(ticket.getTicketNumber())
                      .arg(businessDesc)
                      .arg(m_totalWaiting);

    QMessageBox msgBox(this);
    msgBox.setWindowTitle("Ticket Issued");
    msgBox.setText(message);
    msgBox.setIcon(QMessageBox::Information);
    msgBox.setStandardButtons(QMessageBox::Ok);
    msgBox.exec();
}
