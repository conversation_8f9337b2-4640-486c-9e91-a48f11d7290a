#ifndef MESSAGE_PROTOCOL_H
#define MESSAGE_PROTOCOL_H

#include "ticket.h"
#include <QString>
#include <QStringList>

/**
 * 网络通信协议
 * 定义客户端之间的消息格式和类型
 */
class MessageProtocol
{
public:
    enum MessageType {
        // 取号相关
        TAKE_NUMBER_REQUEST = 1,    // 取号请求
        TAKE_NUMBER_RESPONSE = 2,   // 取号响应
        
        // 叫号相关
        CALL_NEXT_REQUEST = 3,      // 叫下一号请求
        CALL_NEXT_RESPONSE = 4,     // 叫下一号响应
        CALL_CURRENT_REQUEST = 5,   // 重复呼叫请求
        CALL_CURRENT_RESPONSE = 6,  // 重复呼叫响应
        FINISH_CURRENT_REQUEST = 7, // 完成当前号码请求
        FINISH_CURRENT_RESPONSE = 8,// 完成当前号码响应
        
        // 查询相关
        QUEUE_STATUS_REQUEST = 9,   // 队列状态查询请求
        QUEUE_STATUS_RESPONSE = 10, // 队列状态查询响应
        
        // 广播消息
        TICKET_ADDED_BROADCAST = 11,    // 新增票据广播
        TICKET_CALLED_BROADCAST = 12,   // 叫号广播
        TICKET_FINISHED_BROADCAST = 13, // 完成票据广播
        TICKET_EXPIRED_BROADCAST = 14,  // 过期票据广播
        QUEUE_CHANGED_BROADCAST = 15,   // 队列变化广播
        
        // 系统消息
        HEARTBEAT = 16,             // 心跳消息
        ERROR_RESPONSE = 17,        // 错误响应
        
        // 客户端注册
        CLIENT_REGISTER = 18,       // 客户端注册
        CLIENT_UNREGISTER = 19      // 客户端注销
    };
    
    enum ClientType {
        TICKET_MACHINE = 1,     // 取号机
        CALLING_MACHINE = 2     // 叫号机
    };

public:
    MessageProtocol();
    
    // 消息构建
    static QString buildTakeNumberRequest(Ticket::UserType userType, Ticket::BusinessType businessType);
    static QString buildTakeNumberResponse(const Ticket& ticket, bool success = true, const QString& error = "");
    
    static QString buildCallNextRequest(int windowId);
    static QString buildCallNextResponse(const Ticket& ticket, bool success = true, const QString& error = "");
    
    static QString buildCallCurrentRequest(int windowId);
    static QString buildCallCurrentResponse(bool success = true, const QString& error = "");
    
    static QString buildFinishCurrentRequest(int windowId);
    static QString buildFinishCurrentResponse(bool success = true, const QString& error = "");
    
    static QString buildQueueStatusRequest();
    static QString buildQueueStatusResponse(int waitingCount, const QList<Ticket>& tickets);
    
    static QString buildTicketAddedBroadcast(const Ticket& ticket);
    static QString buildTicketCalledBroadcast(const Ticket& ticket, int windowId);
    static QString buildTicketFinishedBroadcast(const Ticket& ticket, int windowId);
    static QString buildTicketExpiredBroadcast(const Ticket& ticket, int windowId);
    static QString buildQueueChangedBroadcast();
    
    static QString buildHeartbeat(ClientType clientType, int clientId);
    static QString buildErrorResponse(const QString& error);
    
    static QString buildClientRegister(ClientType clientType, int clientId, const QString& clientName);
    static QString buildClientUnregister(ClientType clientType, int clientId);
    
    // 消息解析
    static bool parseMessage(const QString& message, MessageType& type, QStringList& params);
    static MessageType getMessageType(const QString& message);
    
    // 参数解析辅助函数
    static bool parseTakeNumberRequest(const QStringList& params, Ticket::UserType& userType, Ticket::BusinessType& businessType);
    static bool parseTakeNumberResponse(const QStringList& params, Ticket& ticket, bool& success, QString& error);
    
    static bool parseCallNextRequest(const QStringList& params, int& windowId);
    static bool parseCallNextResponse(const QStringList& params, Ticket& ticket, bool& success, QString& error);
    
    static bool parseCallCurrentRequest(const QStringList& params, int& windowId);
    static bool parseCallCurrentResponse(const QStringList& params, bool& success, QString& error);
    
    static bool parseFinishCurrentRequest(const QStringList& params, int& windowId);
    static bool parseFinishCurrentResponse(const QStringList& params, bool& success, QString& error);
    
    static bool parseQueueStatusResponse(const QStringList& params, int& waitingCount, QList<Ticket>& tickets);
    
    static bool parseTicketBroadcast(const QStringList& params, Ticket& ticket, int& windowId);
    static bool parseHeartbeat(const QStringList& params, ClientType& clientType, int& clientId);
    static bool parseClientRegister(const QStringList& params, ClientType& clientType, int& clientId, QString& clientName);
    static bool parseClientUnregister(const QStringList& params, ClientType& clientType, int& clientId);
    
    // 工具函数
    static QString encodeTicket(const Ticket& ticket);
    static bool decodeTicket(const QString& ticketStr, Ticket& ticket);
    static QString encodeTicketList(const QList<Ticket>& tickets);
    static bool decodeTicketList(const QString& ticketsStr, QList<Ticket>& tickets);

private:
    static QString buildMessage(MessageType type, const QStringList& params);
    static QStringList splitMessage(const QString& message);
    static QString joinParams(const QStringList& params);
};

#endif // MESSAGE_PROTOCOL_H
