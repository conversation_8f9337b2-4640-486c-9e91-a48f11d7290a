#!/bin/bash

# 修复并编译脚本
echo "=== 修复并重新编译 ==="
echo ""

# 1. 完全清理
echo "1. 完全清理编译环境..."
make clean 2>/dev/null || true
rm -rf bin lib build
rm -f Makefile */Makefile
rm -f */*.o */moc_* */ui_*
echo "   清理完成"
echo ""

# 2. 创建目录结构
echo "2. 创建目录结构..."
mkdir -p bin
mkdir -p lib  
mkdir -p build/common
mkdir -p build/ticket_machine
mkdir -p build/calling_machine
mkdir -p bin/data
mkdir -p bin/data/backup
echo "   目录创建完成"
echo ""

# 3. 显示Qt版本信息
echo "3. Qt环境检查..."
echo "Qt版本:"
qmake -version
echo ""

# 4. 编译公共库
echo "4. 编译公共库..."
cd common
echo "   当前目录: $(pwd)"

# 生成Makefile
echo "   生成Makefile..."
qmake common.pro
if [ $? -ne 0 ]; then
    echo "   ❌ qmake失败"
    cd ..
    exit 1
fi

# 编译
echo "   编译..."
make
RESULT=$?
echo ""

# 检查编译结果
if [ $RESULT -eq 0 ]; then
    echo "   ✅ 公共库编译成功"
    # 检查输出文件
    if [ -f "../lib/libcommon.a" ]; then
        echo "   ✅ 库文件已生成: ../lib/libcommon.a"
        ls -la ../lib/libcommon.a
    else
        echo "   ⚠️  库文件未在预期位置，查找..."
        find . -name "libcommon.a" -o -name "*.a"
    fi
else
    echo "   ❌ 公共库编译失败"
    cd ..
    exit 1
fi

cd ..
echo ""

# 5. 编译取号机
echo "5. 编译取号机..."
cd ticket_machine
echo "   当前目录: $(pwd)"

# 生成Makefile
echo "   生成Makefile..."
qmake ticket_machine.pro
if [ $? -ne 0 ]; then
    echo "   ❌ qmake失败"
    cd ..
    exit 1
fi

# 编译
echo "   编译..."
make
RESULT=$?
echo ""

# 检查编译结果
if [ $RESULT -eq 0 ]; then
    echo "   ✅ 取号机编译成功"
    # 检查输出文件
    if [ -f "../bin/ticket_machine" ]; then
        echo "   ✅ 可执行文件已生成: ../bin/ticket_machine"
        ls -la ../bin/ticket_machine
    else
        echo "   ⚠️  可执行文件未在预期位置，查找..."
        find . -name "ticket_machine" -type f
    fi
else
    echo "   ❌ 取号机编译失败"
    cd ..
    exit 1
fi

cd ..
echo ""

# 6. 编译叫号机
echo "6. 编译叫号机..."
cd calling_machine
echo "   当前目录: $(pwd)"

# 生成Makefile
echo "   生成Makefile..."
qmake calling_machine.pro
if [ $? -ne 0 ]; then
    echo "   ❌ qmake失败"
    cd ..
    exit 1
fi

# 编译
echo "   编译..."
make
RESULT=$?
echo ""

# 检查编译结果
if [ $RESULT -eq 0 ]; then
    echo "   ✅ 叫号机编译成功"
    # 检查输出文件
    if [ -f "../bin/calling_machine" ]; then
        echo "   ✅ 可执行文件已生成: ../bin/calling_machine"
        ls -la ../bin/calling_machine
    else
        echo "   ⚠️  可执行文件未在预期位置，查找..."
        find . -name "calling_machine" -type f
    fi
else
    echo "   ❌ 叫号机编译失败"
    cd ..
    exit 1
fi

cd ..
echo ""

# 7. 最终检查
echo "7. 最终检查..."
echo ""
echo "📁 项目根目录内容:"
ls -la
echo ""

echo "📁 bin/ 目录内容:"
if [ -d "bin" ]; then
    ls -la bin/
else
    echo "bin/ 目录不存在!"
fi
echo ""

echo "📁 lib/ 目录内容:"
if [ -d "lib" ]; then
    ls -la lib/
else
    echo "lib/ 目录不存在!"
fi
echo ""

# 8. 验证可执行文件
echo "8. 验证可执行文件..."
if [ -f "bin/ticket_machine" ]; then
    echo "✅ ticket_machine:"
    file bin/ticket_machine
    echo "   大小: $(ls -lh bin/ticket_machine | awk '{print $5}')"
else
    echo "❌ ticket_machine 不存在"
fi

if [ -f "bin/calling_machine" ]; then
    echo "✅ calling_machine:"
    file bin/calling_machine
    echo "   大小: $(ls -lh bin/calling_machine | awk '{print $5}')"
else
    echo "❌ calling_machine 不存在"
fi

if [ -f "lib/libcommon.a" ]; then
    echo "✅ libcommon.a:"
    file lib/libcommon.a
    echo "   大小: $(ls -lh lib/libcommon.a | awk '{print $5}')"
else
    echo "❌ libcommon.a 不存在"
fi
echo ""

# 9. 成功总结
if [ -f "bin/ticket_machine" ] && [ -f "bin/calling_machine" ] && [ -f "lib/libcommon.a" ]; then
    echo "🎉 编译完全成功!"
    echo ""
    echo "运行方法:"
    echo "   ./bin/ticket_machine                # 启动取号机"
    echo "   ./bin/calling_machine 1             # 启动1号窗口叫号机"
    echo "   ./bin/calling_machine 2             # 启动2号窗口叫号机"
    echo ""
    echo "测试方法:"
    echo "   chmod +x test_system.sh"
    echo "   ./test_system.sh"
else
    echo "⚠️  编译不完整，请检查上面的错误信息"
    echo ""
    echo "调试方法:"
    echo "   ./debug_compile.sh    # 运行调试脚本"
fi

echo ""
echo "=== 编译过程完成 ==="
