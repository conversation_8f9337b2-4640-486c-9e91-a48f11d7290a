#include "calling_machine_window.h"
#include <QApplication>
#include <QTextCodec>
#include <QDir>
#include <QDebug>

int main(int argc, char *argv[])
{
    qDebug() << "Starting calling machine application...";
    QApplication app(argc, argv);
    qDebug() << "QApplication created";

    // 设置应用程序信息
    app.setApplicationName("BankCallingMachine");
    app.setApplicationVersion("1.0");
    app.setOrganizationName("BankQueueSystem");
    qDebug() << "Application info set";

    // 设置中文编码支持 (Qt 4.8.5)
    QTextCodec::setCodecForTr(QTextCodec::codecForName("UTF-8"));
    QTextCodec::setCodecForCStrings(QTextCodec::codecForName("UTF-8"));
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
    qDebug() << "Text codecs set";

    // 确保数据目录存在
    QDir dataDir;
    if (!dataDir.exists("data")) {
        dataDir.mkdir("data");
        qDebug() << "Created data directory";
    } else {
        qDebug() << "Data directory already exists";
    }

    // 获取窗口编号参数
    int windowId = 1;
    if (argc > 1) {
        bool ok;
        int id = QString(argv[1]).toInt(&ok);
        if (ok && id > 0) {
            windowId = id;
        }
    }
    qDebug() << "Window ID:" << windowId;

    // 创建并显示主窗口
    qDebug() << "Creating CallingMachineWindow...";
    CallingMachineWindow window(windowId);
    qDebug() << "CallingMachineWindow created, showing window...";
    window.show();
    qDebug() << "Window shown, starting event loop...";

    return app.exec();
}
